const { execSync } = require('child_process');
const path = require('path');

try {
  console.log('Regenerating Prisma client...');
  
  // Change to the server directory
  process.chdir(__dirname);
  
  // Run prisma generate with the correct schema path
  execSync('npx prisma generate --schema=src/prisma/schema.prisma', { 
    stdio: 'inherit',
    cwd: __dirname
  });
  
  console.log('✅ Prisma client regenerated successfully!');
  console.log('You can now restart the server.');
  
} catch (error) {
  console.error('❌ Error regenerating Prisma client:', error.message);
  process.exit(1);
}
