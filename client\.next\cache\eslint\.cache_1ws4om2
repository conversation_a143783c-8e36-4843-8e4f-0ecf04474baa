[{"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx": "14", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx": "19", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\reset-password\\page.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\reset-password\\ResetPasswordForm.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx": "53", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx": "54", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx": "55", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx": "56", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx": "57", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx": "58", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx": "59", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx": "60", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx": "61", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx": "62", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx": "63", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx": "64", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx": "65", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx": "66", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx": "67", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx": "69", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx": "71", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx": "72", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx": "75", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx": "76", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx": "80", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx": "92", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx": "93", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts": "94", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts": "95", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts": "96", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts": "97", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts": "98", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts": "99", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts": "100", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts": "101", "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx": "102", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts": "103", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts": "104", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts": "105", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts": "106", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts": "107", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts": "108", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts": "110", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts": "111", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts": "112", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts": "113", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts": "114", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts": "115", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts": "116", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts": "117", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx": "118", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts": "119", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts": "120", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts": "121", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts": "122", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx": "126", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx": "127", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx": "128", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts": "129", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx": "130", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx": "131", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx": "132", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts": "133", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx": "134", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx": "135", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts": "136", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts": "137", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts": "138", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx": "139", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx": "140", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\ticketApi.ts": "141", "G:\\UEST\\uest_app\\uest-app\\client\\src\\utils\\pdfGenerator.ts": "142", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx": "143", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts": "144", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\dashboard\\page.tsx": "145", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\layout.tsx": "146", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\login\\page.tsx": "147", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\page.tsx": "148", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\dashboard\\page.tsx": "149", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\layout.tsx": "150", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\login\\page.tsx": "151", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\page.tsx": "152", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\adminticketApi.ts": "153", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts": "154", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx": "155", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx": "156", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx": "157", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx": "158", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts": "159", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts": "160", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts": "161", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts": "162", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts": "163", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts": "164", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx": "165", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx": "166", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx": "167", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts": "168", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx": "169", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx": "170", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx": "171", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts": "172", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts": "173", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx": "174", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx": "175", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx": "176", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts": "177"}, {"size": 156, "mtime": 1751275811335, "results": "178", "hashOfConfig": "179"}, {"size": 4799, "mtime": 1751275807358, "results": "180", "hashOfConfig": "179"}, {"size": 5240, "mtime": 1751275802728, "results": "181", "hashOfConfig": "179"}, {"size": 29458, "mtime": 1751625232828, "results": "182", "hashOfConfig": "179"}, {"size": 3171, "mtime": 1747109292602, "results": "183", "hashOfConfig": "179"}, {"size": 4002, "mtime": 1747109292604, "results": "184", "hashOfConfig": "179"}, {"size": 226, "mtime": 1751024271963, "results": "185", "hashOfConfig": "179"}, {"size": 5928, "mtime": 1751024275820, "results": "186", "hashOfConfig": "179"}, {"size": 2482, "mtime": 1751625232838, "results": "187", "hashOfConfig": "179"}, {"size": 5384, "mtime": 1747289688954, "results": "188", "hashOfConfig": "179"}, {"size": 3658, "mtime": 1747289688955, "results": "189", "hashOfConfig": "179"}, {"size": 674, "mtime": 1747289688955, "results": "190", "hashOfConfig": "179"}, {"size": 5734, "mtime": 1751625232867, "results": "191", "hashOfConfig": "179"}, {"size": 114, "mtime": 1747289688978, "results": "192", "hashOfConfig": "179"}, {"size": 597, "mtime": 1751625232869, "results": "193", "hashOfConfig": "179"}, {"size": 13660, "mtime": 1749201002331, "results": "194", "hashOfConfig": "179"}, {"size": 550, "mtime": 1747289688983, "results": "195", "hashOfConfig": "179"}, {"size": 1623, "mtime": 1747289688984, "results": "196", "hashOfConfig": "179"}, {"size": 3787, "mtime": 1747990267735, "results": "197", "hashOfConfig": "179"}, {"size": 516, "mtime": 1747289689001, "results": "198", "hashOfConfig": "179"}, {"size": 18142, "mtime": 1749201002388, "results": "199", "hashOfConfig": "179"}, {"size": 551, "mtime": 1747289689003, "results": "200", "hashOfConfig": "179"}, {"size": 14780, "mtime": 1747990267862, "results": "201", "hashOfConfig": "179"}, {"size": 557, "mtime": 1747289689005, "results": "202", "hashOfConfig": "179"}, {"size": 4900, "mtime": 1751625232913, "results": "203", "hashOfConfig": "179"}, {"size": 451, "mtime": 1747289689007, "results": "204", "hashOfConfig": "179"}, {"size": 514, "mtime": 1747289689008, "results": "205", "hashOfConfig": "179"}, {"size": 9870, "mtime": 1748962020705, "results": "206", "hashOfConfig": "179"}, {"size": 9250, "mtime": 1749206244199, "results": "207", "hashOfConfig": "179"}, {"size": 578, "mtime": 1747289689027, "results": "208", "hashOfConfig": "179"}, {"size": 19944, "mtime": 1748962020707, "results": "209", "hashOfConfig": "179"}, {"size": 17460, "mtime": 1751710119643, "results": "210", "hashOfConfig": "179"}, {"size": 13775, "mtime": 1749201002401, "results": "211", "hashOfConfig": "179"}, {"size": 1778, "mtime": 1747797160900, "results": "212", "hashOfConfig": "179"}, {"size": 30200, "mtime": 1751625233034, "results": "213", "hashOfConfig": "179"}, {"size": 531, "mtime": 1747109267499, "results": "214", "hashOfConfig": "179"}, {"size": 20383, "mtime": 1747109267515, "results": "215", "hashOfConfig": "179"}, {"size": 650, "mtime": 1747109267537, "results": "216", "hashOfConfig": "179"}, {"size": 3626, "mtime": 1747370468557, "results": "217", "hashOfConfig": "179"}, {"size": 10121, "mtime": 1747624459676, "results": "218", "hashOfConfig": "179"}, {"size": 6100, "mtime": 1747109267569, "results": "219", "hashOfConfig": "179"}, {"size": 354, "mtime": 1747109267605, "results": "220", "hashOfConfig": "179"}, {"size": 4841, "mtime": 1747109267602, "results": "221", "hashOfConfig": "179"}, {"size": 14852, "mtime": 1747109267661, "results": "222", "hashOfConfig": "179"}, {"size": 4990, "mtime": 1749722967811, "results": "223", "hashOfConfig": "179"}, {"size": 4724, "mtime": 1749722967814, "results": "224", "hashOfConfig": "179"}, {"size": 34451, "mtime": 1751625233066, "results": "225", "hashOfConfig": "179"}, {"size": 15866, "mtime": 1749486774681, "results": "226", "hashOfConfig": "179"}, {"size": 1058, "mtime": 1747109268011, "results": "227", "hashOfConfig": "179"}, {"size": 19464, "mtime": 1751625233131, "results": "228", "hashOfConfig": "179"}, {"size": 2942, "mtime": 1747289689132, "results": "229", "hashOfConfig": "179"}, {"size": 2594, "mtime": 1747109292536, "results": "230", "hashOfConfig": "179"}, {"size": 2908, "mtime": 1747624459651, "results": "231", "hashOfConfig": "179"}, {"size": 695, "mtime": 1749485471880, "results": "232", "hashOfConfig": "179"}, {"size": 4253, "mtime": 1747289688716, "results": "233", "hashOfConfig": "179"}, {"size": 9141, "mtime": 1747289688734, "results": "234", "hashOfConfig": "179"}, {"size": 5534, "mtime": 1751625232647, "results": "235", "hashOfConfig": "179"}, {"size": 35619, "mtime": 1751710140359, "results": "236", "hashOfConfig": "179"}, {"size": 1582, "mtime": 1747109267153, "results": "237", "hashOfConfig": "179"}, {"size": 3175, "mtime": 1747289688746, "results": "238", "hashOfConfig": "179"}, {"size": 18291, "mtime": 1747624459652, "results": "239", "hashOfConfig": "179"}, {"size": 17562, "mtime": 1750070278882, "results": "240", "hashOfConfig": "179"}, {"size": 6818, "mtime": 1747654911853, "results": "241", "hashOfConfig": "179"}, {"size": 2125, "mtime": 1747109268031, "results": "242", "hashOfConfig": "179"}, {"size": 4021, "mtime": 1747289689134, "results": "243", "hashOfConfig": "179"}, {"size": 1090, "mtime": 1747109268032, "results": "244", "hashOfConfig": "179"}, {"size": 1634, "mtime": 1747109268033, "results": "245", "hashOfConfig": "179"}, {"size": 2158, "mtime": 1747109268035, "results": "246", "hashOfConfig": "179"}, {"size": 6645, "mtime": 1748363529790, "results": "247", "hashOfConfig": "179"}, {"size": 2003, "mtime": 1747109268037, "results": "248", "hashOfConfig": "179"}, {"size": 1258, "mtime": 1747109268043, "results": "249", "hashOfConfig": "179"}, {"size": 833, "mtime": 1747289689135, "results": "250", "hashOfConfig": "179"}, {"size": 0, "mtime": 1744777321785, "results": "251", "hashOfConfig": "179"}, {"size": 1166, "mtime": 1747624459679, "results": "252", "hashOfConfig": "179"}, {"size": 3914, "mtime": 1747109268044, "results": "253", "hashOfConfig": "179"}, {"size": 8541, "mtime": 1747289689136, "results": "254", "hashOfConfig": "179"}, {"size": 3871, "mtime": 1747109268047, "results": "255", "hashOfConfig": "179"}, {"size": 992, "mtime": 1747109268048, "results": "256", "hashOfConfig": "179"}, {"size": 634, "mtime": 1747109268051, "results": "257", "hashOfConfig": "179"}, {"size": 3738, "mtime": 1747109268052, "results": "258", "hashOfConfig": "179"}, {"size": 2860, "mtime": 1747289689149, "results": "259", "hashOfConfig": "179"}, {"size": 1680, "mtime": 1747109268054, "results": "260", "hashOfConfig": "179"}, {"size": 750, "mtime": 1747109268056, "results": "261", "hashOfConfig": "179"}, {"size": 6382, "mtime": 1747109268057, "results": "262", "hashOfConfig": "179"}, {"size": 738, "mtime": 1747109268059, "results": "263", "hashOfConfig": "179"}, {"size": 4229, "mtime": 1747289689150, "results": "264", "hashOfConfig": "179"}, {"size": 22359, "mtime": 1747289689157, "results": "265", "hashOfConfig": "179"}, {"size": 292, "mtime": 1747109268060, "results": "266", "hashOfConfig": "179"}, {"size": 596, "mtime": 1747109268061, "results": "267", "hashOfConfig": "179"}, {"size": 2564, "mtime": 1747289689158, "results": "268", "hashOfConfig": "179"}, {"size": 2016, "mtime": 1747109268062, "results": "269", "hashOfConfig": "179"}, {"size": 781, "mtime": 1747109268063, "results": "270", "hashOfConfig": "179"}, {"size": 1952, "mtime": 1747289689159, "results": "271", "hashOfConfig": "179"}, {"size": 584, "mtime": 1749468144347, "results": "272", "hashOfConfig": "179"}, {"size": 7273, "mtime": 1747109268066, "results": "273", "hashOfConfig": "179"}, {"size": 1380, "mtime": 1751708406286, "results": "274", "hashOfConfig": "179"}, {"size": 188, "mtime": 1747109268072, "results": "275", "hashOfConfig": "179"}, {"size": 1955, "mtime": 1750649654427, "results": "276", "hashOfConfig": "179"}, {"size": 8579, "mtime": 1751276839017, "results": "277", "hashOfConfig": "179"}, {"size": 465, "mtime": 1747109268078, "results": "278", "hashOfConfig": "179"}, {"size": 824, "mtime": 1747289689165, "results": "279", "hashOfConfig": "179"}, {"size": 310, "mtime": 1747109267096, "results": "280", "hashOfConfig": "179"}, {"size": 1536, "mtime": 1750070278917, "results": "281", "hashOfConfig": "179"}, {"size": 3382, "mtime": 1751255662795, "results": "282", "hashOfConfig": "179"}, {"size": 843, "mtime": 1747109292640, "results": "283", "hashOfConfig": "179"}, {"size": 1282, "mtime": 1751625233134, "results": "284", "hashOfConfig": "179"}, {"size": 2363, "mtime": 1748260878717, "results": "285", "hashOfConfig": "179"}, {"size": 1193, "mtime": 1748964660194, "results": "286", "hashOfConfig": "179"}, {"size": 438, "mtime": 1747109268089, "results": "287", "hashOfConfig": "179"}, {"size": 508, "mtime": 1747109268089, "results": "288", "hashOfConfig": "179"}, {"size": 1564, "mtime": 1747624459698, "results": "289", "hashOfConfig": "179"}, {"size": 1895, "mtime": 1747883078087, "results": "290", "hashOfConfig": "179"}, {"size": 3017, "mtime": 1747289689201, "results": "291", "hashOfConfig": "179"}, {"size": 1144, "mtime": 1747109268090, "results": "292", "hashOfConfig": "179"}, {"size": 414, "mtime": 1747109268091, "results": "293", "hashOfConfig": "179"}, {"size": 484, "mtime": 1747109268092, "results": "294", "hashOfConfig": "179"}, {"size": 590, "mtime": 1747883078088, "results": "295", "hashOfConfig": "179"}, {"size": 232, "mtime": 1747109268096, "results": "296", "hashOfConfig": "179"}, {"size": 1095, "mtime": 1747109268097, "results": "297", "hashOfConfig": "179"}, {"size": 1516, "mtime": 1750649654430, "results": "298", "hashOfConfig": "179"}, {"size": 1132, "mtime": 1750070278919, "results": "299", "hashOfConfig": "179"}, {"size": 458, "mtime": 1749201002646, "results": "300", "hashOfConfig": "179"}, {"size": 10094, "mtime": 1751024283211, "results": "301", "hashOfConfig": "179"}, {"size": 25179, "mtime": 1750070278883, "results": "302", "hashOfConfig": "179"}, {"size": 28459, "mtime": 1751625232977, "results": "303", "hashOfConfig": "179"}, {"size": 4504, "mtime": 1750070278904, "results": "304", "hashOfConfig": "179"}, {"size": 3193, "mtime": 1748962020700, "results": "305", "hashOfConfig": "179"}, {"size": 1643, "mtime": 1747624459680, "results": "306", "hashOfConfig": "179"}, {"size": 3539, "mtime": 1748260878719, "results": "307", "hashOfConfig": "179"}, {"size": 24034, "mtime": 1751625233037, "results": "308", "hashOfConfig": "179"}, {"size": 5982, "mtime": 1750070278915, "results": "309", "hashOfConfig": "179"}, {"size": 433, "mtime": 1749485552367, "results": "310", "hashOfConfig": "179"}, {"size": 466, "mtime": 1747797160907, "results": "311", "hashOfConfig": "179"}, {"size": 53551, "mtime": 1749826192990, "results": "312", "hashOfConfig": "179"}, {"size": 1825, "mtime": 1750070278881, "results": "313", "hashOfConfig": "179"}, {"size": 356, "mtime": 1747883078087, "results": "314", "hashOfConfig": "179"}, {"size": 4161, "mtime": 1748673425653, "results": "315", "hashOfConfig": "179"}, {"size": 2286, "mtime": 1748260878729, "results": "316", "hashOfConfig": "179"}, {"size": 16992, "mtime": 1751625232990, "results": "317", "hashOfConfig": "179"}, {"size": 17122, "mtime": 1748768935829, "results": "318", "hashOfConfig": "179"}, {"size": 1013, "mtime": 1749015983191, "results": "319", "hashOfConfig": "179"}, {"size": 8676, "mtime": 1749015983232, "results": "320", "hashOfConfig": "179"}, {"size": 977, "mtime": 1748768935828, "results": "321", "hashOfConfig": "179"}, {"size": 706, "mtime": 1748768935833, "results": "322", "hashOfConfig": "179"}, {"size": 13253, "mtime": 1749486774676, "results": "323", "hashOfConfig": "179"}, {"size": 3439, "mtime": 1749201002493, "results": "324", "hashOfConfig": "179"}, {"size": 5281, "mtime": 1749201002506, "results": "325", "hashOfConfig": "179"}, {"size": 769, "mtime": 1749201002515, "results": "326", "hashOfConfig": "179"}, {"size": 13324, "mtime": 1749486774678, "results": "327", "hashOfConfig": "179"}, {"size": 3419, "mtime": 1749201002538, "results": "328", "hashOfConfig": "179"}, {"size": 5058, "mtime": 1749201002553, "results": "329", "hashOfConfig": "179"}, {"size": 759, "mtime": 1749201002557, "results": "330", "hashOfConfig": "179"}, {"size": 4130, "mtime": 1749201002631, "results": "331", "hashOfConfig": "179"}, {"size": 523, "mtime": 1749201002641, "results": "332", "hashOfConfig": "179"}, {"size": 2910, "mtime": 1749486774671, "results": "333", "hashOfConfig": "179"}, {"size": 34076, "mtime": 1750649654237, "results": "334", "hashOfConfig": "179"}, {"size": 1279, "mtime": 1749486774674, "results": "335", "hashOfConfig": "179"}, {"size": 40655, "mtime": 1751276982580, "results": "336", "hashOfConfig": "179"}, {"size": 962, "mtime": 1750070278918, "results": "337", "hashOfConfig": "179"}, {"size": 404, "mtime": 1749486774735, "results": "338", "hashOfConfig": "179"}, {"size": 610, "mtime": 1749486774735, "results": "339", "hashOfConfig": "179"}, {"size": 535, "mtime": 1749885108597, "results": "340", "hashOfConfig": "179"}, {"size": 2278, "mtime": 1749486774737, "results": "341", "hashOfConfig": "179"}, {"size": 460, "mtime": 1749486774737, "results": "342", "hashOfConfig": "179"}, {"size": 932, "mtime": 1751276652384, "results": "343", "hashOfConfig": "179"}, {"size": 2443, "mtime": 1751276652402, "results": "344", "hashOfConfig": "179"}, {"size": 58225, "mtime": 1751625232758, "results": "345", "hashOfConfig": "179"}, {"size": 1171, "mtime": 1751276652417, "results": "346", "hashOfConfig": "179"}, {"size": 8203, "mtime": 1751276838980, "results": "347", "hashOfConfig": "179"}, {"size": 535, "mtime": 1750649654200, "results": "348", "hashOfConfig": "179"}, {"size": 5275, "mtime": 1750649654371, "results": "349", "hashOfConfig": "179"}, {"size": 964, "mtime": 1751276652418, "results": "350", "hashOfConfig": "179"}, {"size": 841, "mtime": 1750649654428, "results": "351", "hashOfConfig": "179"}, {"size": 1293, "mtime": 1751625233132, "results": "352", "hashOfConfig": "179"}, {"size": 21787, "mtime": 1751625233018, "results": "353", "hashOfConfig": "179"}, {"size": 11414, "mtime": 1751625232741, "results": "354", "hashOfConfig": "179"}, {"size": 3319, "mtime": 1751625233498, "results": "355", "hashOfConfig": "179"}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uj1650", {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\reset-password\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\reset-password\\ResetPasswordForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx", ["887"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx", [], ["888"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx", [], ["889"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts", [], ["890"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts", [], ["891"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx", [], ["892", "893"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\ticketApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\utils\\pdfGenerator.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\dashboard\\page.tsx", ["894"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\shivwaterpark\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\dashboard\\page.tsx", ["895"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\smwaterpark\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\adminticketApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx", ["896", "897"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx", ["898", "899", "900", "901", "902", "903", "904"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx", ["905"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts", [], [], {"ruleId": "906", "severity": 1, "message": "907", "line": 100, "column": 6, "nodeType": "908", "endLine": 100, "endColumn": 62, "suggestions": "909"}, {"ruleId": "906", "severity": 1, "message": "910", "line": 138, "column": 6, "nodeType": "908", "endLine": 138, "endColumn": 12, "suggestions": "911", "suppressions": "912"}, {"ruleId": "906", "severity": 1, "message": "913", "line": 21, "column": 6, "nodeType": "908", "endLine": 21, "endColumn": 17, "suggestions": "914", "suppressions": "915"}, {"ruleId": "906", "severity": 1, "message": "916", "line": 193, "column": 6, "nodeType": "908", "endLine": 202, "endColumn": 4, "suggestions": "917", "suppressions": "918"}, {"ruleId": "919", "severity": 2, "message": "920", "line": 42, "column": 15, "nodeType": "921", "messageId": "922", "endLine": 42, "endColumn": 22, "suppressions": "923"}, {"ruleId": "906", "severity": 1, "message": "924", "line": 206, "column": 6, "nodeType": "908", "endLine": 206, "endColumn": 8, "suggestions": "925", "suppressions": "926"}, {"ruleId": "906", "severity": 1, "message": "927", "line": 211, "column": 6, "nodeType": "908", "endLine": 211, "endColumn": 42, "suggestions": "928", "suppressions": "929"}, {"ruleId": "906", "severity": 1, "message": "930", "line": 79, "column": 6, "nodeType": "908", "endLine": 79, "endColumn": 33, "suggestions": "931"}, {"ruleId": "906", "severity": 1, "message": "930", "line": 79, "column": 6, "nodeType": "908", "endLine": 79, "endColumn": 33, "suggestions": "932"}, {"ruleId": "906", "severity": 1, "message": "933", "line": 413, "column": 4, "nodeType": "908", "endLine": 420, "endColumn": 2, "suggestions": "934"}, {"ruleId": "906", "severity": 1, "message": "933", "line": 445, "column": 6, "nodeType": "908", "endLine": 445, "endColumn": 120, "suggestions": "935"}, {"ruleId": "906", "severity": 1, "message": "936", "line": 112, "column": 6, "nodeType": "908", "endLine": 112, "endColumn": 28, "suggestions": "937"}, {"ruleId": "906", "severity": 1, "message": "936", "line": 128, "column": 6, "nodeType": "908", "endLine": 128, "endColumn": 28, "suggestions": "938"}, {"ruleId": "906", "severity": 1, "message": "939", "line": 228, "column": 6, "nodeType": "908", "endLine": 228, "endColumn": 77, "suggestions": "940"}, {"ruleId": "906", "severity": 1, "message": "941", "line": 338, "column": 6, "nodeType": "908", "endLine": 338, "endColumn": 132, "suggestions": "942"}, {"ruleId": "906", "severity": 1, "message": "943", "line": 383, "column": 6, "nodeType": "908", "endLine": 383, "endColumn": 45, "suggestions": "944"}, {"ruleId": "906", "severity": 1, "message": "943", "line": 392, "column": 6, "nodeType": "908", "endLine": 392, "endColumn": 45, "suggestions": "945"}, {"ruleId": "906", "severity": 1, "message": "946", "line": 529, "column": 5, "nodeType": "908", "endLine": 529, "endColumn": 112, "suggestions": "947"}, {"ruleId": "906", "severity": 1, "message": "948", "line": 222, "column": 8, "nodeType": "908", "endLine": 222, "endColumn": 82, "suggestions": "949"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'exam.duration'. Either include it or remove the dependency array.", "ArrayExpression", ["950"], "React Hook useEffect has a missing dependency: 'fetchTutors'. Either include it or remove the dependency array.", ["951"], ["952"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["953"], ["954"], "React Hook useEffect has missing dependencies: 'enterFullscreen' and 'isFullscreen'. Either include them or remove the dependency array.", ["955"], ["956"], "prefer-const", "'minutes' is never reassigned. Use 'const' instead.", "Identifier", "useConst", ["957"], "React Hook useEffect has a missing dependency: 'fetchConstants'. Either include it or remove the dependency array.", ["958"], ["959"], "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["960"], ["961"], "React Hook useEffect has a missing dependency: 'currentPage'. Either include it or remove the dependency array.", ["962"], ["963"], "React Hook useEffect has a missing dependency: 'handleNextQuestion'. Either include it or remove the dependency array.", ["964"], ["965"], "React Hook useEffect has a missing dependency: 'initializeViolationCounts'. Either include it or remove the dependency array.", ["966"], ["967"], "React Hook useCallback has missing dependencies: 'isCameraReady', 'isSubmitted', and 'router'. Either include them or remove the dependency array.", ["968"], "React Hook useEffect has a missing dependency: 'userAnswers'. Either include it or remove the dependency array.", ["969"], "React Hook useEffect has a missing dependency: 'exitFullScreen'. Either include it or remove the dependency array.", ["970"], ["971"], "React Hook useCallback has unnecessary dependencies: 'examIdStr' and 'studentId'. Either exclude them or remove the dependency array.", ["972"], "React Hook useEffect has missing dependencies: 'currentRoomId', 'offlineMessageUsers', and 'selectedUserId'. Either include them or remove the dependency array.", ["973"], {"desc": "974", "fix": "975"}, {"desc": "976", "fix": "977"}, {"kind": "978", "justification": "979"}, {"desc": "980", "fix": "981"}, {"kind": "978", "justification": "979"}, {"desc": "982", "fix": "983"}, {"kind": "978", "justification": "979"}, {"kind": "978", "justification": "979"}, {"desc": "984", "fix": "985"}, {"kind": "978", "justification": "979"}, {"desc": "986", "fix": "987"}, {"kind": "978", "justification": "979"}, {"desc": "988", "fix": "989"}, {"desc": "988", "fix": "990"}, {"desc": "991", "fix": "992"}, {"desc": "993", "fix": "994"}, {"desc": "995", "fix": "996"}, {"desc": "995", "fix": "997"}, {"desc": "998", "fix": "999"}, {"desc": "1000", "fix": "1001"}, {"desc": "1002", "fix": "1003"}, {"desc": "1004", "fix": "1005"}, {"desc": "1006", "fix": "1007"}, {"desc": "1008", "fix": "1009"}, "Update the dependencies array to be: [exam.start_date, exam.start_registration_date, exam.id, exam.duration]", {"range": "1010", "text": "1011"}, "Update the dependencies array to be: [fetchTutors, page]", {"range": "1012", "text": "1013"}, "directive", "", "Update the dependencies array to be: [authError, dispatch]", {"range": "1014", "text": "1015"}, "Update the dependencies array to be: [escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", {"range": "1016", "text": "1017"}, "Update the dependencies array to be: [fetchConstants]", {"range": "1018", "text": "1019"}, "Update the dependencies array to be: [currentPage, limit, filtersApplied, fetchQuestions]", {"range": "1020", "text": "1021"}, "Update the dependencies array to be: [searchQuery, fetchTickets, currentPage]", {"range": "1022", "text": "1023"}, {"range": "1024", "text": "1023"}, "Update the dependencies array to be: [questions, currentQuestionIndex, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showTermination, handleNextQuestion]", {"range": "1025", "text": "1026"}, "Update the dependencies array to be: [timeLeft, questions, currentQuestionIndex, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showTermination, handleNextQuestion]", {"range": "1027", "text": "1028"}, "Update the dependencies array to be: [studentId, examIdStr, initializeViolationCounts]", {"range": "1029", "text": "1030"}, {"range": "1031", "text": "1030"}, "Update the dependencies array to be: [isSubmitted, studentId, examIdStr, currentQuestionIndex, questions, isCameraReady, router, selectedAnswer]", {"range": "1032", "text": "1033"}, "Update the dependencies array to be: [currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", {"range": "1034", "text": "1035"}, "Update the dependencies array to be: [isQuizCompleted, studentId, examIdStr, exitFullScreen]", {"range": "1036", "text": "1037"}, "Update the dependencies array to be: [showTermination, studentId, examIdStr, exitFullScreen]", {"range": "1038", "text": "1039"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", {"range": "1040", "text": "1041"}, "Update the dependencies array to be: [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, selectedUserId, offlineMessageUsers, currentRoomId]", {"range": "1042", "text": "1043"}, [3832, 3888], "[exam.start_date, exam.start_registration_date, exam.id, exam.duration]", [4092, 4098], "[fetchTutors, page]", [622, 633], "[auth<PERSON><PERSON><PERSON>, dispatch]", [6888, 7055], "[escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", [7258, 7260], "[fetchConstants]", [7403, 7439], "[currentPage, limit, filtersApplied, fetchQuestions]", [2497, 2524], "[searchQuery, fetchTickets, currentPage]", [2489, 2516], [15058, 15184], "[questions, currentQuestionIndex, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showTermination, handleNextQuestion]", [15908, 16022], "[timeLeft, questions, currentQuestionIndex, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showTermination, handleNextQuestion]", [4165, 4187], "[studentId, examIdStr, initializeViolationCounts]", [4619, 4641], [7719, 7790], "[isSubmitted, studentId, examIdStr, currentQuestionIndex, questions, isCameraReady, router, selectedAnswer]", [11952, 12078], "[currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", [13953, 13992], "[isQuizCompleted, studentId, examIdStr, exitFullScreen]", [14283, 14322], "[showTermination, studentId, examIdStr, exitFullScreen]", [19762, 19869], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", [9400, 9474], "[username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, selectedUserId, offlineMessageUsers, currentRoomId]"]