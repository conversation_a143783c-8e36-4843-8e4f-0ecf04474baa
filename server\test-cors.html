<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test</h1>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';

            try {
                // Test health endpoint
                const response = await fetch('http://localhost:4005/health', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3>✅ CORS Test Successful!</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ CORS Test Failed</h3>
                        <p>Status: ${response.status}</p>
                        <p>Status Text: ${response.statusText}</p>
                    `;
                }

                // Test student admission endpoint
                const admissionResponse = await fetch('http://localhost:4005/api/v1/student-admission/check-gr/TEST123', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                resultDiv.innerHTML += `
                    <h3>Student Admission API Test:</h3>
                    <p>Status: ${admissionResponse.status}</p>
                `;

            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ CORS Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
