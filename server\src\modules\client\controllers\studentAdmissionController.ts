import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import prisma from '@/config/prismaClient';

// Helper function to get authenticated class ID
const getAuthenticatedClassId = (req: Request): string | null => {
  return (req as any).class?.id || null;
};

// Create student details (first step)
export const createStudentDetailsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = getAuthenticatedClassId(req);
    if (!classId) {
      return sendError(res, 'Authentication required', 401);
    }

    const {
      gr_no, family_name, first_name, middle_name, last_name, gender,
      date_of_birth, age, aadhaar_no, blood_group, birth_place, mother_tongue,
      address, city, pin, district, state, country, religion, caste, sub_caste,
      contact_no, email, department, classroom, year, route, waypoint
    } = req.body;

    const photoPath = req.file?.path;

    // Create student profile with admission data
    const studentProfile = await prisma.studentProfile.create({
      data: {
        classId,
        grNo: gr_no,
        familyName: family_name,
        gender,
        dateOfBirth: date_of_birth ? new Date(date_of_birth) : null,
        birthday: date_of_birth ? new Date(date_of_birth) : new Date(),
        age,
        contactNo: contact_no,
        aadhaarNo: aadhaar_no,
        bloodGroup: blood_group,
        photo: photoPath,
        birthPlace: birth_place,
        motherTongue: mother_tongue,
        address,
        city,
        pin,
        district,
        state,
        country,
        religion,
        caste,
        subCaste: sub_caste,
        medium: 'English', // Default value
        classroom,
        school: 'N/A', // Default value
        year,
        route,
        waypoint,
        department,
        status: 'PENDING',
        
        // Create associated student record
        student: {
          create: {
            firstName: first_name,
            lastName: last_name,
            email: email || `${gr_no}@temp.com`,
            contact: contact_no,
            password: 'temp_password',
            isVerified: false,
          }
        }
      },
      include: {
        student: true
      }
    });

    return sendSuccess(res, { id: studentProfile.id, studentId: studentProfile.studentId }, 'Student details saved successfully');
  } catch (error: any) {
    console.error('Error creating student details:', error);
    return sendError(res, error.message || 'Failed to create student details', 500);
  }
};

// Create parent details (second step)
export const createParentDetailsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = getAuthenticatedClassId(req);
    if (!classId) {
      return sendError(res, 'Authentication required', 401);
    }

    const {
      student_id, fathers_name, fathers_middle_name, fathers_last_name,
      fathers_qualification, fathers_occupation, fathers_aadhaar_no,
      mothers_name, mothers_middle_name, mothers_last_name,
      mothers_qualification, mothers_occupation, mothers_aadhaar_no,
      contact_no_1, contact_no_2, family_income, part_of_ngo
    } = req.body;

    if (!student_id) {
      return sendError(res, 'Student ID is required', 400);
    }

    // Update the student profile with parent details
    const updatedProfile = await prisma.studentProfile.update({
      where: { id: student_id },
      data: {
        fathersName: fathers_name,
        fathersMiddleName: fathers_middle_name,
        fathersLastName: fathers_last_name,
        fathersQualification: fathers_qualification,
        fathersOccupation: fathers_occupation,
        fathersAadhaarNo: fathers_aadhaar_no,
        mothersName: mothers_name,
        mothersMiddleName: mothers_middle_name,
        mothersLastName: mothers_last_name,
        mothersQualification: mothers_qualification,
        mothersOccupation: mothers_occupation,
        mothersAadhaarNo: mothers_aadhaar_no,
        contactNo1: contact_no_1,
        contactNo2: contact_no_2,
        familyIncome: family_income,
        partOfNgo: part_of_ngo,
      }
    });

    return sendSuccess(res, { id: updatedProfile.id }, 'Parent details saved successfully');
  } catch (error: any) {
    console.error('Error creating parent details:', error);
    return sendError(res, error.message || 'Failed to create parent details', 500);
  }
};

// Create complete student (single step)
export const createCompleteStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = getAuthenticatedClassId(req);
    if (!classId) {
      return sendError(res, 'Authentication required', 401);
    }

    // This would handle complete student creation in one go
    // Implementation similar to createStudentDetailsController but with all fields
    return sendSuccess(res, {}, 'Complete student created successfully');
  } catch (error: any) {
    console.error('Error creating complete student:', error);
    return sendError(res, error.message || 'Failed to create complete student', 500);
  }
};

// Get all students for a class
export const getAllStudentsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = getAuthenticatedClassId(req);
    if (!classId) {
      return sendError(res, 'Authentication required', 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [students, total] = await Promise.all([
      prisma.studentProfile.findMany({
        where: { classId },
        include: {
          student: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              contact: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.studentProfile.count({ where: { classId } })
    ]);

    return sendSuccess(res, {
      students,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      }
    }, 'Students retrieved successfully');
  } catch (error: any) {
    console.error('Error getting students:', error);
    return sendError(res, error.message || 'Failed to get students', 500);
  }
};

// Get student by ID
export const getStudentByIdController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      return sendError(res, 'Authentication required', 401);
    }

    const student = await prisma.studentProfile.findFirst({
      where: { id, classId },
      include: {
        student: true,
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
          }
        }
      }
    });

    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    return sendSuccess(res, student, 'Student retrieved successfully');
  } catch (error: any) {
    console.error('Error getting student:', error);
    return sendError(res, error.message || 'Failed to get student', 500);
  }
};

// Check GR number availability
export const checkGRNumberController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { grNumber } = req.params;
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      return sendError(res, 'Authentication required', 401);
    }

    const existingStudent = await prisma.studentProfile.findFirst({
      where: {
        grNo: grNumber,
        classId: classId,
      }
    });

    return sendSuccess(res, {
      exists: !!existingStudent,
      available: !existingStudent
    }, existingStudent ? 'GR Number already exists' : 'GR Number is available');
  } catch (error: any) {
    console.error('Error checking GR number:', error);
    return sendError(res, error.message || 'Failed to check GR number', 500);
  }
};
