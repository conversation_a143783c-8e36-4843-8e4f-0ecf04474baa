import { Request as ExpressRequest, Response } from "express";
import {
  createCompleteStudentService,
  createStudentDetailsService,
  createParentDetailsService,
  checkGRNumberService,
  getAllAdmissionsService,
  updateAdmissionStatusService,
} from "../services/studentAdmissionService";
import prisma from "@/config/prismaClient";

interface Request extends ExpressRequest {
  class?: {
    id: string;
    [key: string]: any;
  };
}

// Helper function to get authenticated class ID
const getAuthenticatedClassId = (req: Request): string | null => {
  return req.class?.id || null;
};

export const createCompleteStudent = async (req: Request, res: Response): Promise<void> => {
  try {
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    const studentData = req.body;
    const photoPath = req.file ? req.file.path.replace(/\\/g, "/") : undefined;

    // Check if GR number already exists
    const grCheck = await checkGRNumberService(studentData.studentDetails.gr_no, classId);
    if (grCheck.exists) {
      res.status(400).json({
        success: false,
        message: "GR Number already exists for this class",
      });
      return;
    }

    const result = await createCompleteStudentService(studentData, classId, photoPath);

    res.status(201).json({
      success: true,
      data: result,
      message: "Student created successfully",
    });
  } catch (error: any) {
    console.error('Error in createCompleteStudent:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to create student",
    });
  }
};

export const createStudentDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    const studentData = req.body;
    const photoPath = req.file ? req.file.path.replace(/\\/g, "/") : undefined;

    // Check if GR number already exists
    const grCheck = await checkGRNumberService(studentData.gr_no, classId);
    if (grCheck.exists) {
      res.status(400).json({
        success: false,
        message: "GR Number already exists for this class",
      });
      return;
    }

    const result = await createStudentDetailsService(studentData, classId, photoPath);

    res.status(201).json({
      success: true,
      data: result,
      message: "Student details created successfully",
    });
  } catch (error: any) {
    console.error('Error in createStudentDetails:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to create student details",
    });
  }
};

export const createParentDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const parentData = req.body;
    const studentId = req.body.student_id;

    if (!studentId) {
      res.status(400).json({
        success: false,
        message: "Student ID is required",
      });
      return;
    }

    const result = await createParentDetailsService(parentData, studentId);

    res.status(201).json({
      success: true,
      data: result,
      message: "Parent details created successfully",
    });
  } catch (error: any) {
    console.error('Error in createParentDetails:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to create parent details",
    });
  }
};

export const updateStudentDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    // Implementation for updating student details
    // This would involve UPDATE queries similar to the CREATE operations
    
    res.status(200).json({
      success: true,
      message: "Student details updated successfully",
    });
  } catch (error: any) {
    console.error('Error in updateStudentDetails:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to update student details",
    });
  }
};

export const updateParentDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    // Implementation for updating parent details
    
    res.status(200).json({
      success: true,
      message: "Parent details updated successfully",
    });
  } catch (error: any) {
    console.error('Error in updateParentDetails:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to update parent details",
    });
  }
};

export const getStudentById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    // Get student with all related data using Prisma
    const student = await prisma.studentAdmission.findFirst({
      where: {
        id: id,
        classId: classId,
      },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
          },
        },
      },
    });

    if (!student) {
      res.status(404).json({
        success: false,
        message: "Student not found",
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: student,
    });
  } catch (error: any) {
    console.error('Error in getStudentById:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get student",
    });
  }
};

export const getAllStudents = async (req: Request, res: Response): Promise<void> => {
  try {
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const search = req.query.search as string;

    const result = await getAllAdmissionsService(classId, {
      page,
      limit,
      status,
      search,
    });

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error('Error in getAllStudents:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get students",
    });
  }
};

export const checkGRNumber = async (req: Request, res: Response): Promise<void> => {
  try {
    const { grNumber } = req.params;
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    const result = await checkGRNumberService(grNumber, classId);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error('Error in checkGRNumber:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to check GR number",
    });
  }
};

export const getStudentsByClassroom = async (req: Request, res: Response): Promise<void> => {
  // Implementation for getting students by classroom
  res.status(200).json({ success: true, message: "Not implemented yet" });
};

export const searchStudents = async (req: Request, res: Response): Promise<void> => {
  // Implementation for searching students
  res.status(200).json({ success: true, message: "Not implemented yet" });
};

export const updateAdmissionStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const classId = getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    if (!['PENDING', 'APPROVED', 'REJECTED'].includes(status)) {
      res.status(400).json({
        success: false,
        message: "Invalid status. Must be PENDING, APPROVED, or REJECTED.",
      });
      return;
    }

    const result = await updateAdmissionStatusService(id, status, classId);

    res.status(200).json({
      success: true,
      data: result,
      message: result.message,
    });
  } catch (error: any) {
    console.error('Error in updateAdmissionStatus:', error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to update admission status",
    });
  }
};

export const deleteStudent = async (req: Request, res: Response): Promise<void> => {
  // Implementation for deleting students
  res.status(200).json({ success: true, message: "Not implemented yet" });
};
