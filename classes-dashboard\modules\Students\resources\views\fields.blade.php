<!-- Student Basic Information -->
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('firstName', 'First Name *') !!}
            {!! Form::text('firstName', null, ['class' => 'form-control', 'placeholder' => 'Enter first name']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('lastName', 'Last Name *') !!}
            {!! Form::text('lastName', null, ['class' => 'form-control', 'placeholder' => 'Enter last name']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('email', 'Email Address *') !!}
            {!! Form::email('email', null, ['class' => 'form-control', 'placeholder' => 'Enter email address']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('contact', 'Contact Number') !!}
            {!! Form::text('contact', null, ['class' => 'form-control', 'placeholder' => 'Enter contact number']) !!}
        </div>
    </div>
</div>

<!-- Academic Information -->
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('medium', 'Medium *') !!}
            {!! Form::select('medium', [
                '' => 'Select Medium',
                'English' => 'English',
                'Hindi' => 'Hindi',
                'Gujarati' => 'Gujarati',
                'Other' => 'Other'
            ], null, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('classroom', 'Classroom *') !!}
            {!! Form::text('classroom', null, ['class' => 'form-control', 'placeholder' => 'Enter classroom']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('birthday', 'Birthday *') !!}
            {!! Form::date('birthday', null, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('school', 'School Name *') !!}
            {!! Form::text('school', null, ['class' => 'form-control', 'placeholder' => 'Enter school name']) !!}
        </div>
    </div>
</div>

<!-- Address -->
<div class="form-group">
    {!! Form::label('address', 'Address *') !!}
    {!! Form::textarea('address', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => 'Enter complete address']) !!}
</div>

<!-- File Uploads -->
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('photo', 'Student Photo') !!}
            {!! Form::file('photo', ['class' => 'form-control', 'accept' => 'image/*']) !!}
            <small class="text-muted">Max size: 2MB. Formats: JPG, PNG, GIF</small>
            
            <div class="form-group mt-2">
                <img id="photoPreview" src="#" alt="Photo Preview" 
                     style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; padding: 4px; border-radius: 6px; display: none;" />
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('document', 'Document (Optional)') !!}
            {!! Form::file('document', ['class' => 'form-control', 'accept' => '.pdf,.doc,.docx']) !!}
            <small class="text-muted">Max size: 5MB. Formats: PDF, DOC, DOCX</small>
            
            <div class="form-group mt-2">
                <span id="documentName" style="display: none; color: #28a745; font-weight: bold;"></span>
            </div>
        </div>
    </div>
</div>

<!-- Submit Button -->
<div class="form-group text-right">
    {!! Form::submit('Create Student Profile', ['class' => 'btn btn-primary', 'id' => 'saveStudent']) !!}
    <a href="{{ route('students.index') }}" class="btn btn-secondary ml-2">Cancel</a>
</div>
