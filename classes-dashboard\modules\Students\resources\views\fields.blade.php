<!-- Student Basic Information -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('grNo', 'GR Number') !!}
            {!! Form::text('grNo', null, ['class' => 'form-control', 'placeholder' => 'Enter GR number']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('familyName', 'Family Name') !!}
            {!! Form::text('familyName', null, ['class' => 'form-control', 'placeholder' => 'Enter family name']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('gender', 'Gender *') !!}
            {!! Form::select('gender', [
                '' => 'Select Gender',
                'Male' => 'Male',
                'Female' => 'Female',
                'Other' => 'Other'
            ], null, ['class' => 'form-control']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('firstName', 'First Name *') !!}
            {!! Form::text('firstName', null, ['class' => 'form-control', 'placeholder' => 'Enter first name']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('lastName', 'Last Name *') !!}
            {!! Form::text('lastName', null, ['class' => 'form-control', 'placeholder' => 'Enter last name']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('email', 'Email Address *') !!}
            {!! Form::email('email', null, ['class' => 'form-control', 'placeholder' => 'Enter email address']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('contact', 'Contact Number') !!}
            {!! Form::text('contact', null, ['class' => 'form-control', 'placeholder' => 'Enter contact number']) !!}
        </div>
    </div>
</div>

<!-- Academic Information -->
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('medium', 'Medium *') !!}
            {!! Form::select('medium', [
                '' => 'Select Medium',
                'English' => 'English',
                'Hindi' => 'Hindi',
                'Gujarati' => 'Gujarati',
                'Other' => 'Other'
            ], null, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('classroom', 'Classroom *') !!}
            {!! Form::text('classroom', null, ['class' => 'form-control', 'placeholder' => 'Enter classroom']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('birthday', 'Birthday *') !!}
            {!! Form::date('birthday', null, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('school', 'School Name *') !!}
            {!! Form::text('school', null, ['class' => 'form-control', 'placeholder' => 'Enter school name']) !!}
        </div>
    </div>
</div>

<!-- Personal Details -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('age', 'Age') !!}
            {!! Form::text('age', null, ['class' => 'form-control', 'placeholder' => 'Enter age']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('aadhaarNo', 'Aadhaar Number') !!}
            {!! Form::text('aadhaarNo', null, ['class' => 'form-control', 'placeholder' => 'Enter Aadhaar number']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('bloodGroup', 'Blood Group') !!}
            {!! Form::select('bloodGroup', [
                '' => 'Select Blood Group',
                'A+' => 'A+', 'A-' => 'A-',
                'B+' => 'B+', 'B-' => 'B-',
                'AB+' => 'AB+', 'AB-' => 'AB-',
                'O+' => 'O+', 'O-' => 'O-'
            ], null, ['class' => 'form-control']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('birthPlace', 'Birth Place') !!}
            {!! Form::text('birthPlace', null, ['class' => 'form-control', 'placeholder' => 'Enter birth place']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('motherTongue', 'Mother Tongue') !!}
            {!! Form::text('motherTongue', null, ['class' => 'form-control', 'placeholder' => 'Enter mother tongue']) !!}
        </div>
    </div>
</div>

<!-- Address Information -->
<div class="form-group">
    {!! Form::label('address', 'Address *') !!}
    {!! Form::textarea('address', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => 'Enter complete address']) !!}
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('city', 'City') !!}
            {!! Form::text('city', null, ['class' => 'form-control', 'placeholder' => 'Enter city']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('pin', 'PIN Code') !!}
            {!! Form::text('pin', null, ['class' => 'form-control', 'placeholder' => 'Enter PIN code']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('district', 'District') !!}
            {!! Form::text('district', null, ['class' => 'form-control', 'placeholder' => 'Enter district']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('state', 'State') !!}
            {!! Form::text('state', null, ['class' => 'form-control', 'placeholder' => 'Enter state']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('country', 'Country') !!}
            {!! Form::text('country', null, ['class' => 'form-control', 'placeholder' => 'Enter country', 'value' => 'India']) !!}
        </div>
    </div>
</div>

<!-- Cultural Information -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('religion', 'Religion') !!}
            {!! Form::text('religion', null, ['class' => 'form-control', 'placeholder' => 'Enter religion']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('caste', 'Caste') !!}
            {!! Form::text('caste', null, ['class' => 'form-control', 'placeholder' => 'Enter caste']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('subCaste', 'Sub Caste') !!}
            {!! Form::text('subCaste', null, ['class' => 'form-control', 'placeholder' => 'Enter sub caste']) !!}
        </div>
    </div>
</div>

<!-- Academic Information -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('year', 'Academic Year') !!}
            {!! Form::text('year', null, ['class' => 'form-control', 'placeholder' => 'Enter academic year']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('department', 'Department') !!}
            {!! Form::text('department', null, ['class' => 'form-control', 'placeholder' => 'Enter department']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('route', 'Route') !!}
            {!! Form::text('route', null, ['class' => 'form-control', 'placeholder' => 'Enter route']) !!}
        </div>
    </div>
</div>

<div class="form-group">
    {!! Form::label('waypoint', 'Waypoint') !!}
    {!! Form::text('waypoint', null, ['class' => 'form-control', 'placeholder' => 'Enter waypoint']) !!}
</div>

<!-- Parent Details -->
<h4 class="mt-4 mb-3">Father's Details</h4>
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('fathersName', 'Father\'s First Name') !!}
            {!! Form::text('fathersName', null, ['class' => 'form-control', 'placeholder' => 'Enter father\'s first name']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('fathersMiddleName', 'Father\'s Middle Name') !!}
            {!! Form::text('fathersMiddleName', null, ['class' => 'form-control', 'placeholder' => 'Enter father\'s middle name']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('fathersLastName', 'Father\'s Last Name') !!}
            {!! Form::text('fathersLastName', null, ['class' => 'form-control', 'placeholder' => 'Enter father\'s last name']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('fathersQualification', 'Father\'s Qualification') !!}
            {!! Form::text('fathersQualification', null, ['class' => 'form-control', 'placeholder' => 'Enter father\'s qualification']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('fathersOccupation', 'Father\'s Occupation') !!}
            {!! Form::text('fathersOccupation', null, ['class' => 'form-control', 'placeholder' => 'Enter father\'s occupation']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('fathersAadhaarNo', 'Father\'s Aadhaar Number') !!}
            {!! Form::text('fathersAadhaarNo', null, ['class' => 'form-control', 'placeholder' => 'Enter father\'s Aadhaar number']) !!}
        </div>
    </div>
</div>

<h4 class="mt-4 mb-3">Mother's Details</h4>
<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('mothersName', 'Mother\'s First Name') !!}
            {!! Form::text('mothersName', null, ['class' => 'form-control', 'placeholder' => 'Enter mother\'s first name']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('mothersMiddleName', 'Mother\'s Middle Name') !!}
            {!! Form::text('mothersMiddleName', null, ['class' => 'form-control', 'placeholder' => 'Enter mother\'s middle name']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('mothersLastName', 'Mother\'s Last Name') !!}
            {!! Form::text('mothersLastName', null, ['class' => 'form-control', 'placeholder' => 'Enter mother\'s last name']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('mothersQualification', 'Mother\'s Qualification') !!}
            {!! Form::text('mothersQualification', null, ['class' => 'form-control', 'placeholder' => 'Enter mother\'s qualification']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('mothersOccupation', 'Mother\'s Occupation') !!}
            {!! Form::text('mothersOccupation', null, ['class' => 'form-control', 'placeholder' => 'Enter mother\'s occupation']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            {!! Form::label('mothersAadhaarNo', 'Mother\'s Aadhaar Number') !!}
            {!! Form::text('mothersAadhaarNo', null, ['class' => 'form-control', 'placeholder' => 'Enter mother\'s Aadhaar number']) !!}
        </div>
    </div>
</div>

<!-- Contact Information -->
<h4 class="mt-4 mb-3">Contact Information</h4>
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('contactNo1', 'Primary Contact Number') !!}
            {!! Form::text('contactNo1', null, ['class' => 'form-control', 'placeholder' => 'Enter primary contact number']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('contactNo2', 'Secondary Contact Number') !!}
            {!! Form::text('contactNo2', null, ['class' => 'form-control', 'placeholder' => 'Enter secondary contact number']) !!}
        </div>
    </div>
</div>

<!-- Additional Information -->
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('familyIncome', 'Family Income') !!}
            {!! Form::text('familyIncome', null, ['class' => 'form-control', 'placeholder' => 'Enter family income']) !!}
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('partOfNgo', 'Part of NGO') !!}
            {!! Form::text('partOfNgo', null, ['class' => 'form-control', 'placeholder' => 'Enter NGO details if applicable']) !!}
        </div>
    </div>
</div>

<!-- File Uploads -->
<h4 class="mt-4 mb-3">Documents</h4>
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('photo', 'Student Photo') !!}
            {!! Form::file('photo', ['class' => 'form-control', 'accept' => 'image/*']) !!}
            <small class="text-muted">Max size: 2MB. Formats: JPG, PNG, GIF</small>

            <div class="form-group mt-2">
                <img id="photoPreview" src="#" alt="Photo Preview"
                     style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; padding: 4px; border-radius: 6px; display: none;" />
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            {!! Form::label('document', 'Document (Optional)') !!}
            {!! Form::file('document', ['class' => 'form-control', 'accept' => '.pdf,.doc,.docx']) !!}
            <small class="text-muted">Max size: 5MB. Formats: PDF, DOC, DOCX</small>

            <div class="form-group mt-2">
                <span id="documentName" style="display: none; color: #28a745; font-weight: bold;"></span>
            </div>
        </div>
    </div>
</div>

<!-- Submit Button -->
<div class="form-group text-right">
    {!! Form::submit('Create Student Profile', ['class' => 'btn btn-primary', 'id' => 'saveStudent']) !!}
    <a href="{{ route('students.index') }}" class="btn btn-secondary ml-2">Cancel</a>
</div>
