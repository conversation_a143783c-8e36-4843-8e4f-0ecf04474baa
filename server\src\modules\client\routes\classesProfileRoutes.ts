import express from 'express';
import multer from 'multer';
import {
  updateClassProfile,
  updateDescription,
  updateProfilePhotos,
  postEducation,
  updateExperience,
  postCertificate,
  handleSendForReview,
  createTuitionClassController,
  deleteProfileRecord,
  postEducationByAdmin,
  postExperienceByAdmin,
  postCertificateByAdmin,
  createAddressClassController,
} from '../controllers/classesProfileController';
import validateRequest from '@/middlewares/validateRequest';
import { createClassAboutSchema, deleteRecordSchema } from '../requests/classProfileRequest';
import { dynamicStorage } from '@/utils/upload';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { authMiddleware } from '@/middlewares/adminAuth';


const classesProfileRoutes = express.Router();

const uploadPhotos = multer({ storage: dynamicStorage({ folder: 'photos', classIdKey: 'classId' }) });
const uploadPhotosAdmin = multer({ storage: dynamicStorage({ folder: 'photos', classIdKey: 'classId' }) });
const uploadExperience = multer({ storage: dynamicStorage({ folder: 'experience', classIdKey: 'classId' }) });
const uploadEducation = multer({ storage: dynamicStorage({ folder: 'education', classIdKey: 'classId' }) });

const uploadCertificates = multer({
  storage: dynamicStorage({ folder: 'certificates', classIdKey: 'classId' })
});

classesProfileRoutes.post(
  '/experience',
  authClientMiddleware,
  uploadExperience.any(),
  updateExperience
);

classesProfileRoutes.post(
  '/certificates',
  authClientMiddleware,
  uploadCertificates.any(),
  postCertificate
);

classesProfileRoutes.post(
  '/about',
  authClientMiddleware,
  validateRequest(createClassAboutSchema),
  updateClassProfile
);
classesProfileRoutes.post('/education', authClientMiddleware, uploadEducation.any(), postEducation);
classesProfileRoutes.post(
  '/images',
  authClientMiddleware,
  uploadPhotos.fields([
    { name: 'profilePhoto', maxCount: 1 },
    { name: 'classesLogo', maxCount: 1 },
  ]),
  updateProfilePhotos
);
classesProfileRoutes.post('/description', authClientMiddleware, updateDescription);
classesProfileRoutes.post('/send-for-review/:id', authClientMiddleware, handleSendForReview);
classesProfileRoutes.post('/tuition-classes', authClientMiddleware, createTuitionClassController);

classesProfileRoutes.post('/admin/tuition-classes', authMiddleware, createTuitionClassController);
classesProfileRoutes.post('/admin/:classId/images', authMiddleware, uploadPhotosAdmin.fields([
  { name: 'profilePhoto', maxCount: 1 },
  { name: 'classesLogo', maxCount: 1 },
]), updateProfilePhotos);

// Admin routes for adding records to specific class
classesProfileRoutes.post('/admin/:classId/education', authMiddleware, uploadEducation.any(), postEducationByAdmin);
classesProfileRoutes.post('/admin/:classId/experience', authMiddleware, uploadExperience.any(), postExperienceByAdmin);
classesProfileRoutes.post('/admin/:classId/certificates', authMiddleware, uploadCertificates.any(), postCertificateByAdmin);

classesProfileRoutes.delete(
  '/admin/:type/:id',
  authMiddleware,
  validateRequest(deleteRecordSchema),
  deleteProfileRecord
);

classesProfileRoutes.delete(
  '/:type/:id',
  authClientMiddleware,
  validateRequest(deleteRecordSchema),
  deleteProfileRecord
);
classesProfileRoutes.post('/address', authClientMiddleware, createAddressClassController);

export default classesProfileRoutes;
