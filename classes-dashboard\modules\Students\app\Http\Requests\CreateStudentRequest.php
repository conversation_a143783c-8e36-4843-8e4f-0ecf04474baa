<?php

namespace Students\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'firstName' => 'required|string|min:2|max:255',
            'lastName' => 'required|string|min:2|max:255',
            'email' => 'required|email|max:255',
            'contact' => 'nullable|string|max:20',
            'medium' => 'required|string|max:255',
            'classroom' => 'required|string|max:255',
            'birthday' => 'required|date',
            'school' => 'required|string|min:2|max:255',
            'address' => 'required|string|min:5|max:500',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'document' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'firstName.required' => 'First name is required.',
            'firstName.min' => 'First name must be at least 2 characters.',
            'lastName.required' => 'Last name is required.',
            'lastName.min' => 'Last name must be at least 2 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'medium.required' => 'Medium is required.',
            'classroom.required' => 'Classroom is required.',
            'birthday.required' => 'Birthday is required.',
            'birthday.date' => 'Please enter a valid date.',
            'school.required' => 'School name is required.',
            'school.min' => 'School name must be at least 2 characters.',
            'address.required' => 'Address is required.',
            'address.min' => 'Address must be at least 5 characters.',
            'photo.image' => 'Photo must be an image file.',
            'photo.mimes' => 'Photo must be a file of type: jpeg, png, jpg, gif.',
            'photo.max' => 'Photo may not be greater than 2MB.',
            'document.mimes' => 'Document must be a file of type: pdf, doc, docx.',
            'document.max' => 'Document may not be greater than 5MB.',
        ];
    }
}
