<?php

namespace Students\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // Basic Information
            'grNo' => 'nullable|string|max:255',
            'familyName' => 'nullable|string|max:255',
            'firstName' => 'required|string|min:2|max:255',
            'lastName' => 'required|string|min:2|max:255',
            'gender' => 'nullable|string|in:Male,Female,Other',
            'email' => 'required|email|max:255',
            'contact' => 'nullable|string|max:20',
            'birthday' => 'required|date',
            'age' => 'nullable|string|max:10',

            // Personal Details
            'aadhaarNo' => 'nullable|string|max:20',
            'bloodGroup' => 'nullable|string|max:10',
            'birthPlace' => 'nullable|string|max:255',
            'motherTongue' => 'nullable|string|max:255',

            // Address Information
            'address' => 'required|string|min:5|max:500',
            'city' => 'nullable|string|max:255',
            'pin' => 'nullable|string|max:10',
            'district' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',

            // Cultural Information
            'religion' => 'nullable|string|max:255',
            'caste' => 'nullable|string|max:255',
            'subCaste' => 'nullable|string|max:255',

            // Academic Information
            'medium' => 'required|string|max:255',
            'classroom' => 'required|string|max:255',
            'school' => 'required|string|min:2|max:255',
            'year' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'route' => 'nullable|string|max:255',
            'waypoint' => 'nullable|string|max:255',

            // Parent Details
            'fathersName' => 'nullable|string|max:255',
            'fathersMiddleName' => 'nullable|string|max:255',
            'fathersLastName' => 'nullable|string|max:255',
            'fathersQualification' => 'nullable|string|max:255',
            'fathersOccupation' => 'nullable|string|max:255',
            'fathersAadhaarNo' => 'nullable|string|max:20',

            'mothersName' => 'nullable|string|max:255',
            'mothersMiddleName' => 'nullable|string|max:255',
            'mothersLastName' => 'nullable|string|max:255',
            'mothersQualification' => 'nullable|string|max:255',
            'mothersOccupation' => 'nullable|string|max:255',
            'mothersAadhaarNo' => 'nullable|string|max:20',

            // Contact Information
            'contactNo1' => 'nullable|string|max:20',
            'contactNo2' => 'nullable|string|max:20',

            // Additional Information
            'familyIncome' => 'nullable|string|max:255',
            'partOfNgo' => 'nullable|string|max:255',

            // Files
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'document' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'firstName.required' => 'First name is required.',
            'firstName.min' => 'First name must be at least 2 characters.',
            'lastName.required' => 'Last name is required.',
            'lastName.min' => 'Last name must be at least 2 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'medium.required' => 'Medium is required.',
            'classroom.required' => 'Classroom is required.',
            'birthday.required' => 'Birthday is required.',
            'birthday.date' => 'Please enter a valid date.',
            'school.required' => 'School name is required.',
            'school.min' => 'School name must be at least 2 characters.',
            'address.required' => 'Address is required.',
            'address.min' => 'Address must be at least 5 characters.',
            'photo.image' => 'Photo must be an image file.',
            'photo.mimes' => 'Photo must be a file of type: jpeg, png, jpg, gif.',
            'photo.max' => 'Photo may not be greater than 2MB.',
            'document.mimes' => 'Document must be a file of type: pdf, doc, docx.',
            'document.max' => 'Document may not be greater than 5MB.',
        ];
    }
}
