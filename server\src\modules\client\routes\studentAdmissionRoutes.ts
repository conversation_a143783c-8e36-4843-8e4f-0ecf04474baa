import express from 'express';
import multer from 'multer';
import {
  createStudentDetailsController,
  createParentDetailsController,
  createCompleteStudentController,
  getAllStudentsController,
  getStudentByIdController,
  checkGRNumberController,
} from '../controllers/studentAdmissionController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { dynamicStorage } from '@/utils/upload';

const studentAdmissionRouter = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: dynamicStorage({ folder: 'student-admission', classIdKey: 'classId' }),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    if (file.fieldname === 'photo') {
      // Allow images for photo
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Photo must be an image file'), false);
      }
    } else if (file.fieldname === 'document') {
      // Allow documents
      const allowedMimes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (allowedMimes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Document must be PDF, DOC, or DOCX'), false);
      }
    } else {
      cb(null, true);
    }
  }
});

// Student admission routes for Laravel classes dashboard
studentAdmissionRouter.post('/student-details', authClientMiddleware, upload.single('photo'), createStudentDetailsController);
studentAdmissionRouter.post('/parent-details', authClientMiddleware, createParentDetailsController);
studentAdmissionRouter.post('/complete', authClientMiddleware, upload.fields([
  { name: 'photo', maxCount: 1 },
  { name: 'document', maxCount: 1 }
]), createCompleteStudentController);

// Get routes
studentAdmissionRouter.get('/', authClientMiddleware, getAllStudentsController);
studentAdmissionRouter.get('/:id', authClientMiddleware, getStudentByIdController);

// Utility routes
studentAdmissionRouter.get('/check-gr/:grNumber', authClientMiddleware, checkGRNumberController);

export default studentAdmissionRouter;
