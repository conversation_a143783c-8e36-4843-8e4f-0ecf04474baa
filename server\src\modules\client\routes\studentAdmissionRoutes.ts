import express, { Request as ExpressRequest, Response, NextFunction } from "express";
import multer from "multer";
import {
  createCompleteStudent,
  createStudentDetails,
  createParentDetails,
  updateStudentDetails,
  updateParentDetails,
  getStudentById,
  getAllStudents,
  deleteStudent,
  checkGRNumber,
  getStudentsByClassroom,
  searchStudents,
  updateAdmissionStatus,
} from "../controllers/studentAdmissionController";
import validateRequest from "@/middlewares/validateRequest";
import {
  createStudentDetailsSchema,
  createParentDetailsSchema,
  createCompleteStudentSchema,
  updateStudentDetailsSchema,
  updateParentDetailsSchema,
} from "../requests/studentAdmissionRequest";
import { authClientMiddleware } from "@/middlewares/clientAuth";
import { dynamicStorage } from "@/utils/upload";

const studentAdmissionRouter = express.Router();

// Configure multer for student photo uploads
const uploadStudentPhoto = multer({
  storage: dynamicStorage('student_photos'),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Helper function to handle async routes
const asRequestHandler = (fn: Function) => (req: ExpressRequest, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Student admission routes
studentAdmissionRouter.post(
  "/complete",
  authClientMiddleware,
  uploadStudentPhoto.single("photo"),
  validateRequest(createCompleteStudentSchema),
  asRequestHandler(createCompleteStudent)
);

studentAdmissionRouter.post(
  "/student-details",
  authClientMiddleware,
  uploadStudentPhoto.single("photo"),
  validateRequest(createStudentDetailsSchema),
  asRequestHandler(createStudentDetails)
);

studentAdmissionRouter.post(
  "/parent-details",
  authClientMiddleware,
  validateRequest(createParentDetailsSchema),
  asRequestHandler(createParentDetails)
);

studentAdmissionRouter.put(
  "/student-details/:id",
  authClientMiddleware,
  uploadStudentPhoto.single("photo"),
  validateRequest(updateStudentDetailsSchema),
  asRequestHandler(updateStudentDetails)
);

studentAdmissionRouter.put(
  "/parent-details/:id",
  authClientMiddleware,
  validateRequest(updateParentDetailsSchema),
  asRequestHandler(updateParentDetails)
);

// Get routes
studentAdmissionRouter.get("/", authClientMiddleware, asRequestHandler(getAllStudents));
studentAdmissionRouter.get("/:id", authClientMiddleware, asRequestHandler(getStudentById));
studentAdmissionRouter.get("/classroom/:classroomId", authClientMiddleware, asRequestHandler(getStudentsByClassroom));

// Utility routes
studentAdmissionRouter.get("/check-gr/:grNumber", authClientMiddleware, asRequestHandler(checkGRNumber));
studentAdmissionRouter.post("/search", authClientMiddleware, asRequestHandler(searchStudents));

// Status management
studentAdmissionRouter.patch("/:id/status", authClientMiddleware, asRequestHandler(updateAdmissionStatus));

// Delete route
studentAdmissionRouter.delete("/:id", authClientMiddleware, asRequestHandler(deleteStudent));

export default studentAdmissionRouter;
