import { Server, Socket } from "socket.io";
import { getPendingMessages, createMessage, markMessagesAsRead, markChatAsSeen } from "../services/chatQueryService";
import { PrismaClient } from "@prisma/client";
import { getUnreadCount } from "../../../utils/notifications";

const prisma = new PrismaClient();

const createRoomId = (userId1: string, userId2: string): string => {
  const sortedIds = [userId1, userId2].sort();
  return `room_${sortedIds[0]}_${sortedIds[1]}`;
};

const seenMessages = new Map<string, boolean>();
const activeChats = new Map<string, string>();

export const initializeSocket = (io: Server, onlineUsers: Map<string, { socketId: string; userType: string; userId: string }>) => {
  io.on("connection", (socket: Socket) => {
    socket.on("join", async (data: { username: string, userType: string, userId: string }) => {
      const { username, userType, userId } = data;

      onlineUsers.set(username, { socketId: socket.id, userType, userId });

      // Join notification room for real-time notifications
      const notificationRoom = `${userType.toLowerCase()}_${userId}`;
      socket.join(notificationRoom);

      try {
        const pendingMessages = await getPendingMessages(userId, userType as 'student' | 'class');
        if (pendingMessages.length > 0) {
          pendingMessages.forEach((message: any) => {
            socket.emit("privateMessage", message);
          });
        }
      } catch (error) {
        console.error("Error fetching pending messages:", error);
      }

      try {
        const unreadMessages = await prisma.chatMessage.findMany({
          where: {
            recipientId: userId,
            recipientType: userType as 'student' | 'class',
            isRead: false
          },
          select: {
            senderId: true,
            senderType: true
          }
        });

        const unreadCounts = new Map<string, number>();
        unreadMessages.forEach((message: any) => {
          if (message.senderId !== userId) {
            const currentCount = unreadCounts.get(message.senderId) || 0;
            unreadCounts.set(message.senderId, currentCount + 1);
          }
        });

        const unreadCountsArray = Array.from(unreadCounts.entries()).map(([senderId, count]) => ({
          userId: senderId,
          unreadCount: count
        }));

        socket.emit("unreadCountsData", unreadCountsArray);
      } catch (error) {
        console.error("Error fetching initial unread counts:", error);
      }

      const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
        username,
        userType: data.userType,
        userId: data.userId
      }));
      io.emit("onlineUsers", users);
    });

    socket.on("joinChatRoom", (data: { userId: string, recipientId: string }) => {
      const { userId, recipientId } = data;
      const roomId = createRoomId(userId, recipientId);

      socket.join(roomId);
      activeChats.set(userId, recipientId);

      const otherUserData = Array.from(onlineUsers.entries()).find(([, userData]) => userData.userId === recipientId);
      if (otherUserData) {
        io.to(otherUserData[1].socketId).emit("userStartedViewing", { viewerId: userId });
      }

      socket.emit("roomJoined", { roomId });
    });

    socket.on("leaveChatRoom", (data: { userId: string, recipientId: string }) => {
      const { userId, recipientId } = data;
      const roomId = createRoomId(userId, recipientId);

      socket.leave(roomId);
      activeChats.delete(userId);

      const otherUserData = Array.from(onlineUsers.entries()).find(([, userData]) => userData.userId === recipientId);
      if (otherUserData) {
        io.to(otherUserData[1].socketId).emit("userStoppedViewing", { viewerId: userId });
      }

      socket.emit("roomLeft", { roomId });
    });

    socket.on("sendPrivateMessage", async (messageData: any) => {
      try {
        const { text, senderId, recipientId, senderType, recipientType } = messageData;

        if (!text || !senderId || !recipientId || !senderType || !recipientType) {
          socket.emit("error", { message: "Invalid message data" });
          return;
        }

        if (senderType === recipientType) {
          socket.emit("error", { message: "Cannot send messages to users with the same role" });
          return;
        }

        const message = await createMessage(text, senderId, senderType, recipientId, recipientType);

        let senderName = '';
        let recipientName = '';

        if (senderType === 'student') {
          const student = await prisma.student.findUnique({
            where: { id: senderId },
            select: { firstName: true, lastName: true }
          });
          senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
        } else if (senderType === 'class') {
          const classUser = await prisma.classes.findUnique({
            where: { id: senderId },
            select: { firstName: true, lastName: true }
          });
          senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
        }

        if (recipientId && recipientType === 'student') {
          const student = await prisma.student.findUnique({
            where: { id: recipientId },
            select: { firstName: true, lastName: true }
          });
          recipientName = student ? `${student.firstName} ${student.lastName}` : 'Unknown Student';
        } else if (recipientId && recipientType === 'class') {
          const classUser = await prisma.classes.findUnique({
            where: { id: recipientId },
            select: { firstName: true, lastName: true }
          });
          recipientName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown Class';
        }

        const roomId = createRoomId(senderId, recipientId);
        const isRecipientViewing = activeChats.get(recipientId) === senderId;

        const messageWithNames = {
          ...message,
          sender: senderName,
          recipient: recipientName,
          isRecipientViewing
        };

        io.to(roomId).emit("privateMessage", messageWithNames);

        const recipientData = Array.from(onlineUsers.entries()).find(([, userData]) => userData.userId === recipientId);
        if (recipientData) {
          io.to(recipientData[1].socketId).emit("updateMessageUsers", {
            username: senderName,
            userId: senderId
          });

          if (!isRecipientViewing) {
            try {
              const unreadCount = await prisma.chatMessage.count({
                where: {
                  senderId: senderId,
                  recipientId: recipientId,
                  isRead: false
                }
              });

              io.to(recipientData[1].socketId).emit("unreadCountUpdate", {
                senderId: senderId,
                senderName: senderName,
                unreadCount: unreadCount
              });
            } catch (error) {
              console.error("Error getting unread count:", error);
            }
          }
        }
      } catch (error) {
        console.error("Error saving private message:", error);
        socket.emit("error", { message: "Failed to send message" });
      }
    });

    socket.on("markMessagesAsSeen", async (data: { senderId: string, recipientId: string, messageIds: string[] }) => {
      const { senderId, recipientId, messageIds } = data;
      const recipientActiveWith = activeChats.get(recipientId);
      const isActivelyViewing = recipientActiveWith === senderId;

      if (isActivelyViewing) {
        messageIds.forEach(messageId => {
          seenMessages.set(messageId, true);
        });

        try {
          await markMessagesAsRead(senderId, recipientId);
          const recipientData = Array.from(onlineUsers.entries()).find(([, userData]) => userData.userId === recipientId);
          if (recipientData) {
            io.to(recipientData[1].socketId).emit("unreadCountUpdate", {
              senderId: senderId,
              senderName: "",
              unreadCount: 0
            });
          }
        } catch (error) {
          console.error('Error marking messages as read:', error);
        }

        const senderData = Array.from(onlineUsers.entries()).find(([, userData]) => userData.userId === senderId);
        if (senderData) {
          io.to(senderData[1].socketId).emit("messagesMarkedAsSeen", {
            byUserId: recipientId,
            messageIds: messageIds
          });
        }
      }
    });

    socket.on("getSeenStatus", (data: { messageIds: string[] }) => {
      const { messageIds } = data;
      const seenStatus: { [key: string]: boolean } = {};

      messageIds.forEach(messageId => {
        seenStatus[messageId] = seenMessages.has(messageId);
      });

      socket.emit("seenStatus", seenStatus);
    });

    socket.on("getOnlineUsers", () => {
      const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
        username,
        userType: data.userType,
        userId: data.userId
      }));
      io.emit("onlineUsers", users);
    });

    socket.on("getUnreadCounts", async (data: { userId: string, userType: string }) => {
      try {
        const { userId, userType } = data;
        const unreadMessages = await prisma.chatMessage.findMany({
          where: {
            recipientId: userId,
            recipientType: userType as 'student' | 'class',
            isRead: false
          },
          select: {
            senderId: true,
            senderType: true
          }
        });

        const unreadCounts = new Map<string, number>();
        unreadMessages.forEach((message: any) => {
          if (message.senderId !== userId) {
            const currentCount = unreadCounts.get(message.senderId) || 0;
            unreadCounts.set(message.senderId, currentCount + 1);
          }
        });

        const unreadCountsArray = Array.from(unreadCounts.entries()).map(([senderId, count]) => ({
          userId: senderId,
          unreadCount: count
        }));

        socket.emit("unreadCountsData", unreadCountsArray);
      } catch (error) {
        console.error("Error fetching unread counts:", error);
        socket.emit("error", { message: "Failed to fetch unread counts" });
      }
    });

    // Handle chat seen event
    socket.on("chatSeen", async (data: { userId: string, userType: string, senderId: string }) => {
      try {
        const { userId, userType, senderId } = data;

        // Mark chat as seen and clear notifications
        await markChatAsSeen(userId, userType as 'student' | 'class', senderId);

        // Get updated notification count
        const userTypeEnum = userType === 'student' ? 'STUDENT' : 'CLASS';
        const unreadCount = await getUnreadCount(userId, userTypeEnum as any);

        // Emit updated notification count to user
        const notificationRoom = `${userType.toLowerCase()}_${userId}`;
        io.to(notificationRoom).emit('notificationCountUpdate', { count: unreadCount });

        console.log(`Chat marked as seen: ${userType} ${userId} from ${senderId}`);
      } catch (error) {
        console.error("Error handling chat seen:", error);
        socket.emit("error", { message: "Failed to mark chat as seen" });
      }
    });

    socket.on("disconnect", () => {
      let disconnectedUserId: string | null = null;

      for (const [username, userData] of onlineUsers.entries()) {
        if (userData.socketId === socket.id) {
          disconnectedUserId = userData.userId;
          onlineUsers.delete(username);

          const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
            username,
            userType: data.userType,
            userId: data.userId
          }));
          io.emit("onlineUsers", users);
          break;
        }
      }

      if (disconnectedUserId) {
        const recipientId = activeChats.get(disconnectedUserId);
        activeChats.delete(disconnectedUserId);

        if (recipientId) {
          const otherUserData = Array.from(onlineUsers.entries()).find(([, userData]) => userData.userId === recipientId);
          if (otherUserData) {
            io.to(otherUserData[1].socketId).emit("userStoppedViewing", { viewerId: disconnectedUserId });
          }
        }
      }
    });
  });
};

export default initializeSocket;