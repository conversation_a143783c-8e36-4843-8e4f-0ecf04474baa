<?php

namespace Admission\Models;

use Fees\Models\StudentPayments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentDetails extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_details';

    protected $fillable = [
        'gr_no',
        'uid_no',
        'family_name',
        'first_name',
        'middle_name',
        'last_name',
        'gender',
        'date_of_birth',
        'age',
        'aadhaar_no',
        'blood_group',
        'photo',
        'birth_place',
        'mother_tongue',
        'address',
        'city',
        'pin',
        'district',
        'state',
        'country',
        'religion',
        'caste',
        'sub_caste',
        'contact_no',
        'email',
        'class_uuid'
    ];

    public function getAcademicInfo()
    {
        return $this->belongsTo(StudentAcademicInfo::class, 'id', 'student_id')->withDefault();
    }

    public function getParentInfo()
    {
        return $this->belongsTo(StudentParentsDetails::class, 'id', 'student_id')->withDefault();
    }



    public function getPaymentInfo()
    {
        return $this->belongsTo(StudentPayments::class, 'id', 'student_id')->withDefault();
    }

    public function getAuthIdentifierName()
    {
        return 'id';
    }

    public function getAuthIdentifier()
    {
        return $this->getKey();
    }
}

