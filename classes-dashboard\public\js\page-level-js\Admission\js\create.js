/******/ (() => { // webpackBootstrap
/*!********************************************************!*\
  !*** ./modules/Admission/resources/views/js/create.js ***!
  \********************************************************/
$("#submit-student-details").click(function () {
  studentDetailsSubmit('#student-details-forms', 'student-details');
});
$("#submit-parents-details").click(function () {
  studentDetailsSubmit('#parents-details-forms', 'parents-details');
});
function studentDetailsSubmit(formid, stepType) {
  event.preventDefault();
  var form = $(formid);
  if (form.valid()) {
    var formData = new FormData();
    var serializedForm = form.serializeArray();

    // Add all form fields to FormData
    $.each(serializedForm, function (index, obj) {
      formData.append(obj.name, obj.value);
    });

    // Add file if present
    var fileInput = form.find('input[type="file"]');
    if (fileInput.length > 0 && fileInput[0].files[0]) {
      var file = fileInput[0].files[0];
      formData.append('photo', file);
    }

    // Determine API endpoint based on step
    var apiUrl;
    if (stepType === 'student-details') {
      apiUrl = window.uest_frontend_url + '/api/v1/student-admission/student-details';
    } else if (stepType === 'parents-details') {
      apiUrl = window.uest_frontend_url + '/api/v1/student-admission/parent-details';
    }

    $.ajax({
      url: apiUrl,
      type: "POST",
      data: formData,
      processData: false,
      contentType: false,
      xhrFields: {
        withCredentials: true
      },
      beforeSend: function beforeSend() {
        $('.page-loader').show();
      },
      success: function success(result) {
        if (result.success) {
          $("html, body").animate({
            scrollTop: 0
          }, "slow");
          stepper.next();
          toastr.success(result.message || 'Data saved successfully!');

          if (stepType === 'student-details') {
            // Store student ID for parent details step
            $('[name="student_id"]').val(result.data.id);
            window.currentStudentId = result.data.id;
          } else if (stepType === 'parents-details') {
            // Final step completed
            toastr.success('Student admission completed successfully!');
            setTimeout(function() {
              window.location.href = window.location.origin + '/admission';
            }, 2000);
          }
        } else {
          toastr.error(result.message || 'Something went wrong!');
        }
      },
      complete: function complete() {
        $('.page-loader').hide();
      },
      error: function error(xhr) {
        var message = 'Something went wrong!';
        if (xhr.responseJSON && xhr.responseJSON.message) {
          message = xhr.responseJSON.message;
        } else if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
          showErrors(xhr.responseJSON.errors);
          message = 'Please check the form for errors.';
        }
        toastr.error(message);
        $('.page-loader').hide();
      }
    });
  }
}
$(document).ready(function () {
  var url = window.location.href;
  if (url.indexOf("enq") !== -1) {
    var params = $.extend({}, doAjax_params_default);
    var queryString = url.split('?')[1];
    var enqValue = queryString.split('enq=')[1];
    params["requestType"] = "GET";
    params["url"] = params["url"] = window.location.origin + checkHost() + '/enquiry/' + enqValue;
    ;
    params["successCallbackFunction"] = function successCallbackFunction(result) {
      $('#first_name').val(result.student_first_name);
      $('#middle_name').val(result.student_middle_name);
      $('#last_name').val(result.student_last_name);
      $('#contact_no_1').val(result.contact_number);
      $('#contact_no_1').val(result.contact_number);
      $('#fathers_name').val(result.student_middle_name);
      $('#fathers_last_name').val(result.student_last_name);
      $('#department').val(result.department).trigger('change');
      setTimeout(function () {
        $('#classroom').val(result.classroom).trigger('change');
      }, 1000);
      $('#email').val(result.email);
      $('#student-details-forms').append("<input type='hidden' name='enquiry' value=" + enqValue + ">");
    };
    commonAjax(params);
  }
});
$('#waypoint').change(function () {
  if ($(this).val()) {
    var params = $.extend({}, doAjax_params_default);
    params['url'] = window.location.origin + checkHost() + "/student/routes/" + $(this).val();
    params['requestType'] = "GET";
    params['successCallbackFunction'] = function successCallbackFunction(response) {
      var select = $('.route-data');
      select.empty();
      select.append($('<option value="">Select Routes</option>'));
      $.each(response.routes, function (index, value) {
        select.append($('<option value="' + value.id + '">' + value.route_name + '</option>'));
      });
    };
    commonAjax(params);
  }
});
function getParentDetails() {
  var params = $.extend({}, doAjax_params_default);
  params["requestType"] = "POST";
  params["url"] = window.location.origin + checkHost() + '/student-parent-details';
  params['data'] = {
    'contact_no': $('#contact_no').val(),
    'student_id': $('[name="student_id"]').val()
  };
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    if (result.success) {
      $.each(result.parentdetail, function (fieldName, value) {
        if (fieldName != 'student_id') {
          $('[name=' + fieldName + ']').val(value);
          $('[name=' + fieldName + ']').attr("readonly", "readonly");
        }
      });
    }
  };
  commonAjax(params);
}
$('#gr_no').change(function () {
  if ($(this).val() && $(this).valid()) {
    $.ajax({
      url: window.uest_frontend_url + '/api/v1/student-admission/check-gr/' + $(this).val(),
      type: 'GET',
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        $('#gr_no').parent().find('.invalid-feedback').remove();
        if (response.data && response.data.exists) {
          $('#gr_no').addClass('is-invalid');
          $('#gr_no').after('<div class="invalid-feedback">GR Number already exists</div>');
        } else {
          $('#gr_no').removeClass('is-invalid');
          $('#gr_no').parent().find('.invalid-feedback').remove();
        }
      },
      error: function(xhr) {
        console.error('Error checking GR number:', xhr);
      }
    });
  }
});
$('#date_of_birth').change(function () {
  var age = moment().diff(moment($(this).val(), "YYYY-MM-DD"), 'years');
  $("#age").val(age);
});
$("#date_of_birth").datepicker({
  dateFormat: "yy-mm-dd",
  changeMonth: true,
  changeYear: true
});
/******/ })()
;