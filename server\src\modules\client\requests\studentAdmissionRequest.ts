import { z } from 'zod';

// Student Details Schema
export const createStudentDetailsSchema = z.object({
  // Academic Information
  department: z.string().min(1, 'Department is required'),
  classroom: z.string().min(1, 'Classroom is required'),
  year: z.string().min(1, 'Year is required'),
  
  // Basic Information
  gr_no: z.string().min(1, 'GR Number is required'),
  family_name: z.string().optional(),
  first_name: z.string().min(1, 'First name is required'),
  middle_name: z.string().min(1, 'Middle name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  gender: z.enum(['Male', 'Female', 'Other']),
  date_of_birth: z.string().min(1, 'Date of birth is required'),
  age: z.string().optional(),
  
  // Contact Information
  contact_no: z.string().min(10, 'Contact number must be at least 10 digits'),
  email: z.string().email('Valid email is required'),
  
  // Personal Details
  aadhaar_no: z.string().optional(),
  blood_group: z.enum(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', '']).optional(),
  birth_place: z.string().optional(),
  mother_tongue: z.string().optional(),
  
  // Address Information
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  pin: z.string().optional(),
  district: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  
  // Cultural Information
  religion: z.string().optional(),
  caste: z.string().optional(),
  sub_caste: z.string().optional(),
  
  // Photo (will be handled separately for file upload)
  photo: z.any().optional(),
  
  // Route Information (if applicable)
  route: z.string().optional(),
  waypoint: z.string().optional(),
});

// Parent Details Schema
export const createParentDetailsSchema = z.object({
  // Father's Information
  fathers_name: z.string().min(1, 'Father\'s name is required'),
  fathers_middle_name: z.string().min(1, 'Father\'s middle name is required'),
  fathers_last_name: z.string().min(1, 'Father\'s last name is required'),
  fathers_qualification: z.string().optional(),
  fathers_occupation: z.string().optional(),
  fathers_aadhaar_no: z.string().optional(),
  
  // Mother's Information
  mothers_name: z.string().min(1, 'Mother\'s name is required'),
  mothers_middle_name: z.string().min(1, 'Mother\'s middle name is required'),
  mothers_last_name: z.string().min(1, 'Mother\'s last name is required'),
  mothers_qualification: z.string().optional(),
  mothers_occupation: z.string().optional(),
  mothers_aadhaar_no: z.string().optional(),
  
  // Contact Information
  contact_no_1: z.string().min(10, 'Primary contact number is required'),
  contact_no_2: z.string().optional(),
  
  // Additional Information
  family_income: z.string().optional(),
  part_of_ngo: z.string().optional(),
});

// Combined Schema for Complete Student Admission
export const createCompleteStudentSchema = z.object({
  studentDetails: createStudentDetailsSchema,
  parentDetails: createParentDetailsSchema,
});

// Update schemas (for editing existing students)
export const updateStudentDetailsSchema = createStudentDetailsSchema.partial().extend({
  student_id: z.string().min(1, 'Student ID is required'),
});

export const updateParentDetailsSchema = createParentDetailsSchema.partial().extend({
  student_id: z.string().min(1, 'Student ID is required'),
});

// Type exports
export type CreateStudentDetailsInput = z.infer<typeof createStudentDetailsSchema>;
export type CreateParentDetailsInput = z.infer<typeof createParentDetailsSchema>;
export type CreateCompleteStudentInput = z.infer<typeof createCompleteStudentSchema>;
export type UpdateStudentDetailsInput = z.infer<typeof updateStudentDetailsSchema>;
export type UpdateParentDetailsInput = z.infer<typeof updateParentDetailsSchema>;
