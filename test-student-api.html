<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Student API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test Student Admission API</h1>
    
    <form id="testForm">
        <h3>Student Details</h3>
        <div class="form-group">
            <label>GR Number:</label>
            <input type="text" name="gr_no" value="TEST001" required>
        </div>
        <div class="form-group">
            <label>First Name:</label>
            <input type="text" name="first_name" value="John" required>
        </div>
        <div class="form-group">
            <label>Middle Name:</label>
            <input type="text" name="middle_name" value="Doe" required>
        </div>
        <div class="form-group">
            <label>Last Name:</label>
            <input type="text" name="last_name" value="Smith" required>
        </div>
        <div class="form-group">
            <label>Gender:</label>
            <select name="gender" required>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
            </select>
        </div>
        <div class="form-group">
            <label>Date of Birth:</label>
            <input type="date" name="date_of_birth" value="2000-01-01" required>
        </div>
        <div class="form-group">
            <label>Contact Number:</label>
            <input type="text" name="contact_no" value="1234567890" required>
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>Address:</label>
            <textarea name="address" required>123 Test Street</textarea>
        </div>
        <div class="form-group">
            <label>City:</label>
            <input type="text" name="city" value="Test City" required>
        </div>
        <div class="form-group">
            <label>Department:</label>
            <input type="text" name="department" value="Science" required>
        </div>
        <div class="form-group">
            <label>Classroom:</label>
            <input type="text" name="classroom" value="10A" required>
        </div>
        <div class="form-group">
            <label>Year:</label>
            <input type="text" name="year" value="2024" required>
        </div>

        <h3>Parent Details</h3>
        <div class="form-group">
            <label>Father's Name:</label>
            <input type="text" name="fathers_name" value="Robert" required>
        </div>
        <div class="form-group">
            <label>Father's Middle Name:</label>
            <input type="text" name="fathers_middle_name" value="James" required>
        </div>
        <div class="form-group">
            <label>Father's Last Name:</label>
            <input type="text" name="fathers_last_name" value="Smith" required>
        </div>
        <div class="form-group">
            <label>Mother's Name:</label>
            <input type="text" name="mothers_name" value="Mary" required>
        </div>
        <div class="form-group">
            <label>Mother's Middle Name:</label>
            <input type="text" name="mothers_middle_name" value="Jane" required>
        </div>
        <div class="form-group">
            <label>Mother's Last Name:</label>
            <input type="text" name="mothers_last_name" value="Smith" required>
        </div>
        <div class="form-group">
            <label>Primary Contact:</label>
            <input type="text" name="contact_no_1" value="9876543210" required>
        </div>

        <button type="submit">Test API</button>
    </form>

    <div id="result"></div>

    <script>
        $('#testForm').on('submit', function(e) {
            e.preventDefault();
            
            // Collect form data
            let formData = new FormData();
            let studentData = {};
            let parentData = {};
            
            // Student fields
            const studentFields = ['gr_no', 'first_name', 'middle_name', 'last_name', 'gender', 
                                 'date_of_birth', 'contact_no', 'email', 'address', 'city', 
                                 'department', 'classroom', 'year'];
            
            // Parent fields
            const parentFields = ['fathers_name', 'fathers_middle_name', 'fathers_last_name',
                                'mothers_name', 'mothers_middle_name', 'mothers_last_name', 'contact_no_1'];
            
            // Collect student data
            studentFields.forEach(field => {
                studentData[field] = $(`[name="${field}"]`).val();
            });
            
            // Collect parent data
            parentFields.forEach(field => {
                parentData[field] = $(`[name="${field}"]`).val();
            });
            
            // Add to FormData
            formData.append('studentDetails', JSON.stringify(studentData));
            formData.append('parentDetails', JSON.stringify(parentData));
            
            // Make API call
            $.ajax({
                url: 'http://localhost:3000/api/v1/student-admission/complete',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhrFields: {
                    withCredentials: true
                },
                success: function(result) {
                    $('#result').html(`
                        <div class="result success">
                            <h4>Success!</h4>
                            <p>${result.message}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `);
                },
                error: function(err) {
                    $('#result').html(`
                        <div class="result error">
                            <h4>Error!</h4>
                            <p>Status: ${err.status}</p>
                            <pre>${JSON.stringify(err.responseJSON, null, 2)}</pre>
                        </div>
                    `);
                }
            });
        });
    </script>
</body>
</html>
