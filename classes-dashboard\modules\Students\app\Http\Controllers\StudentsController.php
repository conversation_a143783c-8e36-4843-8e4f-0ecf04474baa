<?php

namespace Students\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Students\Http\Requests\CreateStudentRequest;

class StudentsController extends Controller
{
    public function index(Request $request)
    {
        // This will show the list of students (fetched from Node.js API)
        return view('Students::index');
    }

    public function create()
    {
        // Show the student creation form
        return view('Students::create');
    }

    public function store(CreateStudentRequest $request)
    {
        // This method exists for Laravel validation but actual storage 
        // happens via AJAX to Node.js API
        return response()->json(['success' => 'Student Created Successfully!!']);
    }

    public function edit($id)
    {
        // Show edit form (data will be fetched from Node.js API)
        return view('Students::edit', compact('id'));
    }

    public function update(CreateStudentRequest $request, $id)
    {
        // This method exists for Laravel validation but actual update 
        // happens via AJAX to Node.js API
        return response()->json(['success' => 'Student Updated Successfully!!']);
    }

    public function destroy($id)
    {
        // Delete will be handled via AJAX to Node.js API
        return response()->json(['success' => 'Student Deleted Successfully!!']);
    }
}
