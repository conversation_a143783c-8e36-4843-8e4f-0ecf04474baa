@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-12 main-title-flex">
                    <h1>Add New Student</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body">
                        {!! Form::open(['route' => 'students.store', 'id' => 'createstudent_form', 'enctype' => 'multipart/form-data']) !!}
                        @include('Students::fields')
                        {!! Form::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('scripts')
    {!! JsValidator::formRequest('Students\Http\Requests\CreateStudentRequest', '#createstudent_form') !!}
    <script>
        var createStudentRoute = {
            store: "{{ route('students.store') }}",
        };

        $(document).ready(function() {
            // Photo preview functionality
            $('#photo').on('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#photoPreview').attr('src', e.target.result).show();
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#photoPreview').hide();
                }
            });

            // Document preview functionality
            $('#document').on('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    $('#documentName').text(file.name).show();
                } else {
                    $('#documentName').hide();
                }
            });

            // Form submission
            $('#createstudent_form').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                if ($(this).valid()) {
                    $.ajax({
                        url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/student-profile/from-class',
                        method: 'POST',
                        data: formData,
                        contentType: false,
                        processData: false,
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function(response) {
                            toastr.success('Student profile created successfully!');
                            $('#photoPreview').hide();
                            $('#documentName').hide();

                            setTimeout(() => {
                                window.location.href = "{{ route('students.index') }}";
                            }, 1000);
                        },
                        error: function(xhr, status, error) {
                            const message = xhr.responseJSON?.message ||
                                'Failed to create student profile.';
                            toastr.error(message);
                            console.error('Error:', xhr, error);
                        }
                    });
                }
            });
        });
    </script>
@endsection
