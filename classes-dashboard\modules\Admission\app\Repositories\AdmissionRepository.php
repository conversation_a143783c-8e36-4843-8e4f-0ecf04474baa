<?php

namespace Admission\Repositories;

use Admission\Interfaces\AdmissionInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Admission\Models\LeavingCerificate;
use Admission\Models\StudentAcademicInfo;
use Admission\Models\StudentDetails;
use Admission\Models\StudentDetailsView;
use Admission\Models\StudentParentsDetails;

use Admission\Models\WaypointRouteLog;
use Enquiry\Models\Enquiry;
use Fees\Models\StudentPayments;
use Document\Models\Document;

class AdmissionRepository implements AdmissionInterface
{
    protected $studentDetails;
    protected $studentParentsDetails;

    protected $studentAcademicInfo;
    protected $studentPayments;
    protected $leavingCerificate;
    protected $studentDetailsView;
    protected $waypointRouteLog;

    public function __construct(
        StudentDetails $studentDetails,
        StudentParentsDetails $studentParentsDetails,
        StudentAcademicInfo $studentAcademicInfo,
        StudentPayments $studentPayments,
        LeavingCerificate $leavingCerificate,
        StudentDetailsView $studentDetailsView,
        WaypointRouteLog $waypointRouteLog
    ) {
        $this->studentDetails = $studentDetails;
        $this->studentParentsDetails = $studentParentsDetails;
        $this->studentAcademicInfo = $studentAcademicInfo;
        $this->studentPayments = $studentPayments;
        $this->leavingCerificate = $leavingCerificate;
        $this->studentDetailsView = $studentDetailsView;
        $this->waypointRouteLog = $waypointRouteLog;
    }

    public function getAll($data)
    {
        $list = $this->studentDetailsView::when($data['department'], function ($query) use ($data) {
            return $query->where('department_id', $data['department']);
        })->when($data['classroom'], function ($query) use ($data) {
            return $query->where('classroom_id', $data['classroom']);
        })->when($data['status'], function ($query) use ($data) {
            return $query->where('status', $data['status']);
        })->when(isset($data['search_key']) && $data['search_key'] != "", function ($query) use ($data) {
            return $query->where('first_name', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('last_name', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('middle_name', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('gr_no', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('contact_no', 'like', '%' . $data['search_key'] . '%');
        })->when($data['vehicle_id'], function ($query) use ($data) {
            $query->whereHas('getRoutes', function ($query) use ($data) {
                return $query->where('vehicle_id', $data['vehicle_id']);
            });
        })->when($data['waypoint_id'], function ($query) use ($data) {
            return $query->where('waypoint_id', $data['waypoint_id']);
        })->when($data['route_id'], function ($query) use ($data) {
            return $query->where('route_id', $data['route_id']);
        })->when($data['student_id'], function ($query) use ($data) {
            return $query->where('id', $data['student_id']);
        })->where('year_id', getActiveYearId())
        ->where('student_class_uuid', Auth::id())
            ->select(
                'student_details_view.*',
                DB::raw("CONCAT(student_details_view.first_name, ' ' , student_details_view.middle_name , ' ', student_details_view.last_name) AS student_full_name"),
                DB::raw("CONCAT(student_details_view.class_name, ' (', student_details_view.department_name, ')') AS class_info"),
            );

        $concatenatedColumns = ['student_full_name', 'class_info'];
        searchColumn($data->input('columns'), $list, $concatenatedColumns);
        orderColumn($data, $list, 'student_details_view.id');

        return $list;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('photo', function ($data) {
                $subdomain = tenantData('subdomain');
                $img = ($data->photo != null && $data->photo != "undefined") ?  asset("storage/{$subdomain}/student_img/" . $data->photo) : tenantData('tenantLogo');
                return '<img style="width: 40px !important;height: 1% !important;" src="' . $img . '" class="img-circle elevation-2">';
            })
            ->addColumn('created_at', function ($data) {
                return date_formatter($data->created_at);
            })
            ->addColumn('student_full_name', function ($data) {
                return $data->student_full_name;
            })
            ->addColumn('contact_no', function ($data) {
                return $data->contact_no;
            })
            ->addColumn('year_name', function ($data) {
                return  $data->year_name;
            })
            ->addColumn('class_info', function ($data) {
                return  $data->class_info;
            })
            ->addColumn('gr_no', function ($data) {
                return $data->gr_no;
            })
            ->addColumn('check_student_transfered', function ($data) {
                return $this->checkStudentTransfered($data->gr_no);
            })
            ->addColumn('action', function ($data) {
                $button = '';
                $button .= '<a href="' . route('studentfee', $data->id) . '" class="btn"><i class="fa fa-rupee-sign"></i></a>';

                $button .= '<a href="' . route('student.show', $data->id) . '" class="btn"><i class="fas fa-eye"></i></a>';

                $button .= '<a href="' . route('student.edit', $data->id) . '" class="btn"><i class="fas fa-edit"></i></a>';

                return $button;
            })->rawColumns(['action', 'photo', 'check_student_transfered'])
            ->make(true);
    }

    public function storeStudentDetails($data)
    {
        $data['class_uuid'] = Auth::id();
        if ($data['photo'] != 'undefined' && $data['photo'] != '') {
            $file = $data['photo'];
            $fileName = time() . '_' . $file->getClientOriginalName();
            $subdomain = request()->getHost();

            $file->storeAs("public/{$subdomain}/student_img", $fileName);
            $data['photo']  = $fileName;
        } else {
            unset($data['photo']);
        }

        if (isset($data['enquiry'])) {
            $enq = Enquiry::find($data['enquiry']);
            $enq->status = config('constants.STATUS.ADMITTED');
            $enq->save();
        }
        $entry = $this->studentDetails::updateOrCreate(
            ['id' => $data['student_id']],
            $data
        );

        $this->storeStudentAcademicInfo($data, $entry->id);
        return $entry->id;
    }

    public function storeStudentAcademicInfo($data, $id)
    {
        $data['student_id'] = $id;
        $this->studentAcademicInfo::updateOrCreate(
            ['student_id' => $data['student_id']],
            $data
        );
    }

    public function storeStudentParentsDetails($data)
    {
        $this->studentParentsDetails::updateOrCreate(
            ['student_id' => $data['student_id']],
            $data
        );
    }



    public function findByid($id)
    {
        return $this->studentDetails::with('getAcademicInfo', 'getParentInfo')->find($id);
    }

    public function checkGRNO($grno, $year, $studentId = null)
    {
        return $this->studentDetails::where('gr_no', $grno)->whereHas('getAcademicInfo', function ($query) use ($year) {
            $query->when($year, function ($query) use ($year) {
                return $query->where('year', $year)->where('status', "ACTIVE");
            });
        })->when($studentId != null, function ($query) use ($studentId) {
            return $query->where('id', '!=', $studentId);
        })->exists();
    }

    public function getStudentParentDetailsFromContactNo($data)
    {
        return $this->studentDetails::with('getParentInfo')->where('contact_no', $data['contact_no'])->where('id', '!=', $data['student_id'])->first();
    }

    public function searchStudent($data)
    {
        $data['status'] = "ACTIVE";
        $list = $this->getAll($data);
        return $this->getDatatable($list);
    }

    public function generateLeavingCertificate($data, $student_id)
    {
        $this->leavingCerificate::updateOrCreate(
            ['student_id' => $student_id],
            $data
        );

        $ainfo = $this->studentAcademicInfo->where('student_id', $student_id)->first();
        $ainfo->status = "INACTIVE";
        $ainfo->save();
    }

    public function getLeavingCertificate($id)
    {
        return $this->leavingCerificate::where('student_id', $id)->first();
    }

    public function activeInactive($student_id)
    {
        $sinfo = $this->studentAcademicInfo->where('student_id', $student_id)->first();
        $sinfo->status = $sinfo->status == config('constants.STATUS.INACTIVE') ?
            config('constants.STATUS.ACTIVE') : config('constants.STATUS.INACTIVE');
        $sinfo->save();
    }

    public function transferStudents($data)
    {
        foreach ($data['id'] as $student_id) {
            $student = $this->findByid($student_id)->toArray();

            $checkStudentExist = $this->checkGRNO($student['gr_no'], $data['year']);
            if (!$checkStudentExist) {
                $newStudent = $this->studentDetails::create($student);

                $student['department'] = $data['department'];
                $student['classroom'] = $data['classroom'];
                $student['year'] = $data['year'];
                $student['waypoint'] = $data['waypoint'];
                $student['route'] = $data['route'];

                $this->studentAcademicInfo::Create(
                    [
                        'student_id' => $newStudent->id,
                        'department' => $data['department'],
                        'classroom' => $data['classroom'],
                        'year' => $data['year'],
                        'waypoint' => $data['waypoint'],
                        'route' =>  $data['route'],
                    ],
                );

                $student['get_parent_info']['student_id'] = $newStudent->id;

                $this->storeStudentParentsDetails($student['get_parent_info']);

                return ['success' => 'Student Transfered Successfully!!'];
            } else {
                return ['error' => 'Student already exist in transfer year!!'];
            }
        }
    }

    public function getAllStudentByClassAndDepartment($data)
    {
        $students = $this->studentDetailsView
            ->where('department_id', $data['department_id'])
            ->where('classroom_id', $data['classroom_id'])
            ->where('status', 'ACTIVE')->where('year_id', getActiveYearId())->get();
        return $students;
    }

    public function checkStudentTransfered($gr_no)
    {
        $activeYear = getActiveYearId();
        $studentDetails = $this->studentDetailsView::where('gr_no', $gr_no)->where('year_id', '>', $activeYear)->exists();

        return $studentDetails;
    }

    //API Functions
    public function findByGrno($grNo)
    {
        $student = $this->studentDetails::with(
            'getAcademicInfo',
            'getAcademicInfo.getDepartment',
            'getAcademicInfo.getClassroom',
            'getAcademicInfo.getYear',
            'getParentInfo',
            'getSiblingsInfo',
            'getHealthInfo',
            'getPastInfo'
        )
            ->orderBy('id', 'DESC')
            ->where('gr_no', $grNo)
            ->first();

        if (!$student) {
            return null;
        }
        $studentData = [
            'personal_details' => [
                'gr_no' => $student->gr_no,
                'first_name' => $student->first_name,
                'middle_name' => $student->middle_name,
                'last_name' => $student->last_name,
                'gender' => $student->gender,
                'date_of_birth' => $student->date_of_birth,
                'mother_tongue' => $student->mother_tongue,
                'address' => $student->address,
                'pin' => $student->pin,
                'religion' => $student->religion,
                'caste' => $student->caste,
                'contact_no' => $student->contact_no,
                'email' => $student->email,
                'photo' => $student->photo
            ],
            'academic_info' => [
                'department' => $student->getAcademicInfo->getDepartment->name ?? null,
                'classroom' => $student->getAcademicInfo->getClassroom->class_name ?? null,
                'year' => $student->getAcademicInfo->getYear->year_name ?? null,
                'status' => $student->getAcademicInfo->status,
                'class_teacher_name' => $student->getAcademicInfo->getClassroom->getClassTeacher->name ?? null,
                'class_teacher_contact' => $student->getAcademicInfo->getClassroom->getClassTeacher->mobile_number ?? null,
            ],
            'parent_info' => [
                'father_name' => trim($student->getParentInfo->fathers_name . ' ' .
                    $student->getParentInfo->fathers_middle_name . ' ' .
                    $student->getParentInfo->fathers_last_name),
                'mother_name' => trim($student->getParentInfo->mothers_name . ' ' .
                    $student->getParentInfo->mothers_middle_name . ' ' .
                    $student->getParentInfo->mothers_last_name),
                'contact_no_1' => $student->getParentInfo->contact_no_1,
                'contact_no_2' => $student->getParentInfo->contact_no_2,
                'part_of_ngo' => $student->getParentInfo->part_of_ngo
            ],
            'siblings_info' => [
                'sibling_name' => $student->getSiblingsInfo->sibling_name,
                'studying_std' => $student->getSiblingsInfo->studying_std,
                'school_name' => $student->getSiblingsInfo->school_name
            ],
            'health_info' => [
                'eye_sight' => $student->getHealthInfo->eye_sight,
                'hear_ability' => $student->getHealthInfo->hear_ability,
                'allergy_1' => $student->getHealthInfo->allergy_1,
                'allergy_2' => $student->getHealthInfo->allergy_2,
                'any_health_issue' => $student->getHealthInfo->any_health_issue,
                'doctors_name' => $student->getHealthInfo->doctors_name
            ],
            'past_info' => [
                'prev_standard' => $student->getPastInfo->prev_standard,
                'prev_school' => $student->getPastInfo->prev_school,
                'prev_passing_year' => $student->getPastInfo->prev_passing_year,
                'prev_school_left_date' => $student->getPastInfo->prev_school_left_date,
                'left_reason' => $student->getPastInfo->left_reason
            ]
        ];

        return $studentData;
    }

    public function updateRoutes($data, $id)
    {
        $this->studentAcademicInfo::updateOrCreate(
            ['student_id' => $id],
            [
                'waypoint' => $data['waypoint'],
                'route' => $data['route']
            ]
        );
    }

    public function logWaypointRouteChange($student_id, array $data)
    {
        $studentAcademic = StudentAcademicInfo::where('student_id', $student_id)->first();
        $oldWaypointId = $studentAcademic->waypoint ?? null;
        $oldRouteId = $studentAcademic->route ?? null;

                // Check if the new values match the current StudentAcademicInfo
        $shouldLog = !$studentAcademic ||
            $studentAcademic->waypoint != $data['waypoint'] ||
            $studentAcademic->route != $data['route'];

        if (!$shouldLog) {
            return;
        }

        $latestLog = WaypointRouteLog::where('student_id', $student_id)
            ->orderBy('created_at', 'desc')
            ->first();

        if (
            $latestLog &&
            $latestLog->new_waypoint_id == $data['waypoint'] &&
            $latestLog->new_route_id == $data['route']
        ) {
            return;
        }

        $user = Auth::user();
        $createdBy = $user
            ? (trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? '')) ?: 'Unknown User')
            : 'System';

        $waypoints = new WaypointRouteLog();
        $waypoints->student_id = $student_id;
        $waypoints->old_waypoint_id = $oldWaypointId;
        $waypoints->new_waypoint_id = $data['waypoint'];
        $waypoints->old_route_id = $oldRouteId;
        $waypoints->new_route_id = $data['route'];
        $waypoints->created_by = $createdBy;

        $waypoints->save();

        return $waypoints;
    }

    public function getWaypointRouteLogs($studentId)
    {
        return WaypointRouteLog::where('student_id', $studentId)
            ->with(['oldWaypoint', 'oldRoute', 'newWaypoint', 'newRoute'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getStudentDocuments($studentId)
    {
        return Document::where('student_id', $studentId)->get();
    }
}
