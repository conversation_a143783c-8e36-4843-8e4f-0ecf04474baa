import express from "express";
import { Server } from "socket.io";
import dotenv from "dotenv";
import { createChatRoutes } from "../modules/client/routes/chatRoutes";
import { initializeSocket } from "../modules/client/controllers/socketController";

dotenv.config();

const app = express();
const allowedOrigins = [
  process.env.FRONTEND_URL || "http://localhost:3000",
  process.env.ADMIN_URL || "http://localhost:3001",
  process.env.DASHBOARD_URL || "http://localhost:3002",
  "http://localhost:3000",
  "http://localhost:3001",
  "http://localhost:3002",
  "http://127.0.0.1:3000",
  "http://127.0.0.1:3001",
  "http://127.0.0.1:3002"
];

const io = new Server(
  {
    cors: {
      origin: (origin, callback) => {
        if (!origin) return callback(null, true);

        if (allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          console.log(`Socket CORS blocked origin: ${origin}`);
          callback(new Error("Not allowed by CORS"));
        }
      },
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
      credentials: true,
    },
    transports: ['websocket', 'polling'],
    allowEIO3: true,
    pingTimeout: 60000,
    pingInterval: 25000,
    path: '/uapi/socket.io',
  }
);

const onlineUsers = new Map<string, { socketId: string; userType: string; userId: string }>();
const router = createChatRoutes(onlineUsers);

initializeSocket(io, onlineUsers);

export { io, app,router, onlineUsers };