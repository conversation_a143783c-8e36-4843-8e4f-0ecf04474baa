import prisma from "@/config/prismaClient";
import {
  CreateStudentDetailsInput,
  CreateParentDetailsInput,
  CreateCompleteStudentInput,
  UpdateStudentDetailsInput,
  UpdateParentDetailsInput,
} from "../requests/studentAdmissionRequest";

// Note: Since we're using the existing Laravel database structure,
// we'll need to work with the existing tables through raw queries or
// create a bridge between the Node.js API and Laravel database

interface StudentFilterOptions {
  page?: number;
  limit?: number;
  department?: string;
  classroom?: string;
  status?: string;
  search?: string;
}

export const createCompleteStudentService = async (
  data: CreateCompleteStudentInput,
  classId: string,
  photoPath?: string
) => {
  try {
    const { studentDetails, parentDetails } = data;

    // Create student admission record using Prisma
    const studentAdmission = await prisma.studentAdmission.create({
      data: {
        classId,

        // Basic Information
        grNo: studentDetails.gr_no,
        familyName: studentDetails.family_name || null,
        firstName: studentDetails.first_name,
        middleName: studentDetails.middle_name,
        lastName: studentDetails.last_name,
        gender: studentDetails.gender,
        dateOfBirth: new Date(studentDetails.date_of_birth),
        age: studentDetails.age || null,

        // Contact Information
        contactNo: studentDetails.contact_no,
        email: studentDetails.email,

        // Personal Details
        aadhaarNo: studentDetails.aadhaar_no || null,
        bloodGroup: studentDetails.blood_group || null,
        photo: photoPath || null,
        birthPlace: studentDetails.birth_place || null,
        motherTongue: studentDetails.mother_tongue || null,

        // Address Information
        address: studentDetails.address,
        city: studentDetails.city,
        pin: studentDetails.pin || null,
        district: studentDetails.district || null,
        state: studentDetails.state || null,
        country: studentDetails.country || null,

        // Cultural Information
        religion: studentDetails.religion || null,
        caste: studentDetails.caste || null,
        subCaste: studentDetails.sub_caste || null,

        // Academic Information
        department: studentDetails.department,
        classroom: studentDetails.classroom,
        year: studentDetails.year,
        route: studentDetails.route || null,
        waypoint: studentDetails.waypoint || null,

        // Parent Details
        fathersName: parentDetails.fathers_name,
        fathersMiddleName: parentDetails.fathers_middle_name,
        fathersLastName: parentDetails.fathers_last_name,
        fathersQualification: parentDetails.fathers_qualification || null,
        fathersOccupation: parentDetails.fathers_occupation || null,
        fathersAadhaarNo: parentDetails.fathers_aadhaar_no || null,

        mothersName: parentDetails.mothers_name,
        mothersMiddleName: parentDetails.mothers_middle_name,
        mothersLastName: parentDetails.mothers_last_name,
        mothersQualification: parentDetails.mothers_qualification || null,
        mothersOccupation: parentDetails.mothers_occupation || null,
        mothersAadhaarNo: parentDetails.mothers_aadhaar_no || null,

        // Contact Information
        contactNo1: parentDetails.contact_no_1,
        contactNo2: parentDetails.contact_no_2 || null,

        // Additional Information
        familyIncome: parentDetails.family_income || null,
        partOfNgo: parentDetails.part_of_ngo || null,
      },
    });

    return {
      id: studentAdmission.id,
      message: 'Student created successfully',
    };
  } catch (error) {
    console.error('Error creating student:', error);
    throw new Error('Failed to create student');
  }
};

export const createStudentDetailsService = async (
  data: CreateStudentDetailsInput,
  classId: string,
  photoPath?: string
) => {
  try {
    // Create partial student admission record (student details only)
    const studentAdmission = await prisma.studentAdmission.create({
      data: {
        classId,

        // Basic Information
        grNo: data.gr_no,
        familyName: data.family_name || null,
        firstName: data.first_name,
        middleName: data.middle_name,
        lastName: data.last_name,
        gender: data.gender,
        dateOfBirth: new Date(data.date_of_birth),
        age: data.age || null,

        // Contact Information
        contactNo: data.contact_no,
        email: data.email,

        // Personal Details
        aadhaarNo: data.aadhaar_no || null,
        bloodGroup: data.blood_group || null,
        photo: photoPath || null,
        birthPlace: data.birth_place || null,
        motherTongue: data.mother_tongue || null,

        // Address Information
        address: data.address,
        city: data.city,
        pin: data.pin || null,
        district: data.district || null,
        state: data.state || null,
        country: data.country || null,

        // Cultural Information
        religion: data.religion || null,
        caste: data.caste || null,
        subCaste: data.sub_caste || null,

        // Academic Information
        department: data.department,
        classroom: data.classroom,
        year: data.year,
        route: data.route || null,
        waypoint: data.waypoint || null,

        // Parent Details (placeholder values - will be updated later)
        fathersName: '',
        fathersMiddleName: '',
        fathersLastName: '',
        mothersName: '',
        mothersMiddleName: '',
        mothersLastName: '',
        contactNo1: '',
      },
    });

    return {
      id: studentAdmission.id,
      message: 'Student details created successfully',
    };
  } catch (error) {
    console.error('Error creating student details:', error);
    throw new Error('Failed to create student details');
  }
};

export const createParentDetailsService = async (
  data: CreateParentDetailsInput,
  studentId: string
) => {
  try {
    // Update the existing student admission record with parent details
    const updatedStudent = await prisma.studentAdmission.update({
      where: { id: studentId },
      data: {
        // Parent Details
        fathersName: data.fathers_name,
        fathersMiddleName: data.fathers_middle_name,
        fathersLastName: data.fathers_last_name,
        fathersQualification: data.fathers_qualification || null,
        fathersOccupation: data.fathers_occupation || null,
        fathersAadhaarNo: data.fathers_aadhaar_no || null,

        mothersName: data.mothers_name,
        mothersMiddleName: data.mothers_middle_name,
        mothersLastName: data.mothers_last_name,
        mothersQualification: data.mothers_qualification || null,
        mothersOccupation: data.mothers_occupation || null,
        mothersAadhaarNo: data.mothers_aadhaar_no || null,

        // Contact Information
        contactNo1: data.contact_no_1,
        contactNo2: data.contact_no_2 || null,

        // Additional Information
        familyIncome: data.family_income || null,
        partOfNgo: data.part_of_ngo || null,
      },
    });

    return {
      message: 'Parent details created successfully',
    };
  } catch (error) {
    console.error('Error creating parent details:', error);
    throw new Error('Failed to create parent details');
  }
};

export const checkGRNumberService = async (grNumber: string, classId: string) => {
  try {
    const existingStudent = await prisma.studentAdmission.findFirst({
      where: {
        grNo: grNumber,
        classId: classId,
      },
    });

    return {
      exists: !!existingStudent,
      message: existingStudent ? 'GR Number already exists' : 'GR Number is available',
    };
  } catch (error) {
    console.error('Error checking GR number:', error);
    throw new Error('Failed to check GR number');
  }
};
