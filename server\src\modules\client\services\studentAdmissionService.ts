import prisma from "@/config/prismaClient";
import {
  CreateStudentDetailsInput,
  CreateParentDetailsInput,
  CreateCompleteStudentInput,
  UpdateStudentDetailsInput,
  UpdateParentDetailsInput,
} from "../requests/studentAdmissionRequest";

// Note: Since we're using the existing Laravel database structure,
// we'll need to work with the existing tables through raw queries or
// create a bridge between the Node.js API and Laravel database

interface StudentFilterOptions {
  page?: number;
  limit?: number;
  department?: string;
  classroom?: string;
  status?: string;
  search?: string;
}

export const createCompleteStudentService = async (
  data: CreateCompleteStudentInput,
  classId: string,
  photoPath?: string
) => {
  try {
    // Since we're working with <PERSON><PERSON>'s database structure,
    // we'll use raw SQL queries to insert into the existing tables
    
    const { studentDetails, parentDetails } = data;
    
    // Add class_uuid and photo path to student details
    const studentData = {
      ...studentDetails,
      class_uuid: classId,
      photo: photoPath || null,
    };

    // Start a transaction
    return await prisma.$transaction(async (tx) => {
      // Insert student details using raw SQL to match Laravel table structure
      const studentResult = await tx.$executeRaw`
        INSERT INTO student_details (
          gr_no, family_name, first_name, middle_name, last_name, gender,
          date_of_birth, age, aadhaar_no, blood_group, photo, birth_place,
          mother_tongue, address, city, pin, district, state, country,
          religion, caste, sub_caste, contact_no, email, class_uuid,
          created_at, updated_at
        ) VALUES (
          ${studentData.gr_no}, ${studentData.family_name}, ${studentData.first_name},
          ${studentData.middle_name}, ${studentData.last_name}, ${studentData.gender},
          ${studentData.date_of_birth}, ${studentData.age}, ${studentData.aadhaar_no},
          ${studentData.blood_group}, ${studentData.photo}, ${studentData.birth_place},
          ${studentData.mother_tongue}, ${studentData.address}, ${studentData.city},
          ${studentData.pin}, ${studentData.district}, ${studentData.state},
          ${studentData.country}, ${studentData.religion}, ${studentData.caste},
          ${studentData.sub_caste}, ${studentData.contact_no}, ${studentData.email},
          ${studentData.class_uuid}, NOW(), NOW()
        )
      `;

      // Get the inserted student ID
      const insertedStudent = await tx.$queryRaw<Array<{ id: number }>>`
        SELECT id FROM student_details WHERE gr_no = ${studentData.gr_no} 
        AND class_uuid = ${classId} ORDER BY id DESC LIMIT 1
      `;

      if (!insertedStudent.length) {
        throw new Error('Failed to create student');
      }

      const studentId = insertedStudent[0].id;

      // Insert academic info
      await tx.$executeRaw`
        INSERT INTO student_academic_info (
          student_id, department, classroom, year, route, waypoint,
          status, created_at, updated_at
        ) VALUES (
          ${studentId}, ${studentData.department}, ${studentData.classroom},
          ${studentData.year}, ${studentData.route}, ${studentData.waypoint},
          'ACTIVE', NOW(), NOW()
        )
      `;

      // Insert parent details
      await tx.$executeRaw`
        INSERT INTO student_parents_details (
          student_id, fathers_name, fathers_middle_name, fathers_last_name,
          fathers_aadhaar_no, fathers_qualification, fathers_occupation,
          mothers_name, mothers_middle_name, mothers_last_name,
          mothers_aadhaar_no, mothers_qualification, mothers_occupation,
          contact_no_1, contact_no_2, family_income, part_of_ngo,
          created_at, updated_at
        ) VALUES (
          ${studentId}, ${parentDetails.fathers_name}, ${parentDetails.fathers_middle_name},
          ${parentDetails.fathers_last_name}, ${parentDetails.fathers_aadhaar_no},
          ${parentDetails.fathers_qualification}, ${parentDetails.fathers_occupation},
          ${parentDetails.mothers_name}, ${parentDetails.mothers_middle_name},
          ${parentDetails.mothers_last_name}, ${parentDetails.mothers_aadhaar_no},
          ${parentDetails.mothers_qualification}, ${parentDetails.mothers_occupation},
          ${parentDetails.contact_no_1}, ${parentDetails.contact_no_2},
          ${parentDetails.family_income}, ${parentDetails.part_of_ngo},
          NOW(), NOW()
        )
      `;

      return {
        id: studentId,
        message: 'Student created successfully',
      };
    });
  } catch (error) {
    console.error('Error creating student:', error);
    throw new Error('Failed to create student');
  }
};

export const createStudentDetailsService = async (
  data: CreateStudentDetailsInput,
  classId: string,
  photoPath?: string
) => {
  try {
    const studentData = {
      ...data,
      class_uuid: classId,
      photo: photoPath || null,
    };

    const studentResult = await prisma.$executeRaw`
      INSERT INTO student_details (
        gr_no, family_name, first_name, middle_name, last_name, gender,
        date_of_birth, age, aadhaar_no, blood_group, photo, birth_place,
        mother_tongue, address, city, pin, district, state, country,
        religion, caste, sub_caste, contact_no, email, class_uuid,
        created_at, updated_at
      ) VALUES (
        ${studentData.gr_no}, ${studentData.family_name}, ${studentData.first_name},
        ${studentData.middle_name}, ${studentData.last_name}, ${studentData.gender},
        ${studentData.date_of_birth}, ${studentData.age}, ${studentData.aadhaar_no},
        ${studentData.blood_group}, ${studentData.photo}, ${studentData.birth_place},
        ${studentData.mother_tongue}, ${studentData.address}, ${studentData.city},
        ${studentData.pin}, ${studentData.district}, ${studentData.state},
        ${studentData.country}, ${studentData.religion}, ${studentData.caste},
        ${studentData.sub_caste}, ${studentData.contact_no}, ${studentData.email},
        ${studentData.class_uuid}, NOW(), NOW()
      )
    `;

    // Get the inserted student ID
    const insertedStudent = await prisma.$queryRaw<Array<{ id: number }>>`
      SELECT id FROM student_details WHERE gr_no = ${studentData.gr_no} 
      AND class_uuid = ${classId} ORDER BY id DESC LIMIT 1
    `;

    if (!insertedStudent.length) {
      throw new Error('Failed to create student');
    }

    const studentId = insertedStudent[0].id;

    // Insert academic info
    await prisma.$executeRaw`
      INSERT INTO student_academic_info (
        student_id, department, classroom, year, route, waypoint,
        status, created_at, updated_at
      ) VALUES (
        ${studentId}, ${studentData.department}, ${studentData.classroom},
        ${studentData.year}, ${studentData.route}, ${studentData.waypoint},
        'ACTIVE', NOW(), NOW()
      )
    `;

    return {
      id: studentId,
      message: 'Student details created successfully',
    };
  } catch (error) {
    console.error('Error creating student details:', error);
    throw new Error('Failed to create student details');
  }
};

export const createParentDetailsService = async (
  data: CreateParentDetailsInput,
  studentId: number
) => {
  try {
    await prisma.$executeRaw`
      INSERT INTO student_parents_details (
        student_id, fathers_name, fathers_middle_name, fathers_last_name,
        fathers_aadhaar_no, fathers_qualification, fathers_occupation,
        mothers_name, mothers_middle_name, mothers_last_name,
        mothers_aadhaar_no, mothers_qualification, mothers_occupation,
        contact_no_1, contact_no_2, family_income, part_of_ngo,
        created_at, updated_at
      ) VALUES (
        ${studentId}, ${data.fathers_name}, ${data.fathers_middle_name},
        ${data.fathers_last_name}, ${data.fathers_aadhaar_no},
        ${data.fathers_qualification}, ${data.fathers_occupation},
        ${data.mothers_name}, ${data.mothers_middle_name},
        ${data.mothers_last_name}, ${data.mothers_aadhaar_no},
        ${data.mothers_qualification}, ${data.mothers_occupation},
        ${data.contact_no_1}, ${data.contact_no_2},
        ${data.family_income}, ${data.part_of_ngo},
        NOW(), NOW()
      )
    `;

    return {
      message: 'Parent details created successfully',
    };
  } catch (error) {
    console.error('Error creating parent details:', error);
    throw new Error('Failed to create parent details');
  }
};

export const checkGRNumberService = async (grNumber: string, classId: string) => {
  try {
    const existingStudent = await prisma.$queryRaw<Array<{ count: number }>>`
      SELECT COUNT(*) as count FROM student_details 
      WHERE gr_no = ${grNumber} AND class_uuid = ${classId}
    `;

    return {
      exists: existingStudent[0].count > 0,
      message: existingStudent[0].count > 0 ? 'GR Number already exists' : 'GR Number is available',
    };
  } catch (error) {
    console.error('Error checking GR number:', error);
    throw new Error('Failed to check GR number');
  }
};
