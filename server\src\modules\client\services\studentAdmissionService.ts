import prisma from "@/config/prismaClient";
import {
  CreateStudentDetailsInput,
  CreateParentDetailsInput,
  CreateCompleteStudentInput,
  UpdateStudentDetailsInput,
  UpdateParentDetailsInput,
} from "../requests/studentAdmissionRequest";

interface StudentFilterOptions {
  page?: number;
  limit?: number;
  department?: string;
  classroom?: string;
  status?: string;
  search?: string;
}

// Service to create Student record from approved admission (temporarily disabled)
export const createStudentFromAdmissionService = async (admissionId: string) => {
  try {
    // TODO: Implement after Prisma client is regenerated
    throw new Error('Service temporarily unavailable - Prisma client needs regeneration');
  } catch (error) {
    console.error('Error creating student from admission:', error);
    throw new Error('Failed to create student account');
  }
};

export const createCompleteStudentService = async (
  data: CreateCompleteStudentInput,
  classId: string,
  photoPath?: string
) => {
  try {
    const { studentDetails, parentDetails } = data;

    // Generate UUID for the new record
    const studentId = require('crypto').randomUUID();

    // Create student admission record using raw SQL (temporary fix)
    await prisma.$executeRaw`
      INSERT INTO "StudentAdmission" (
        id, "classId", "grNo", "familyName", "firstName", "middleName", "lastName",
        gender, "dateOfBirth", age, "contactNo", email, "aadhaarNo", "bloodGroup",
        photo, "birthPlace", "motherTongue", address, city, pin, district, state,
        country, religion, caste, "subCaste", department, classroom, year, route,
        waypoint, "fathersName", "fathersMiddleName", "fathersLastName",
        "fathersQualification", "fathersOccupation", "fathersAadhaarNo",
        "mothersName", "mothersMiddleName", "mothersLastName", "mothersQualification",
        "mothersOccupation", "mothersAadhaarNo", "contactNo1", "contactNo2",
        "familyIncome", "partOfNgo", status, "createdAt", "updatedAt"
      ) VALUES (
        ${studentId}, ${classId}, ${studentDetails.gr_no}, ${studentDetails.family_name},
        ${studentDetails.first_name}, ${studentDetails.middle_name}, ${studentDetails.last_name},
        ${studentDetails.gender}, ${new Date(studentDetails.date_of_birth)}, ${studentDetails.age},
        ${studentDetails.contact_no}, ${studentDetails.email}, ${studentDetails.aadhaar_no},
        ${studentDetails.blood_group}, ${photoPath}, ${studentDetails.birth_place},
        ${studentDetails.mother_tongue}, ${studentDetails.address}, ${studentDetails.city},
        ${studentDetails.pin}, ${studentDetails.district}, ${studentDetails.state},
        ${studentDetails.country}, ${studentDetails.religion}, ${studentDetails.caste},
        ${studentDetails.sub_caste}, ${studentDetails.department}, ${studentDetails.classroom},
        ${studentDetails.year}, ${studentDetails.route}, ${studentDetails.waypoint},
        ${parentDetails.fathers_name}, ${parentDetails.fathers_middle_name},
        ${parentDetails.fathers_last_name}, ${parentDetails.fathers_qualification},
        ${parentDetails.fathers_occupation}, ${parentDetails.fathers_aadhaar_no},
        ${parentDetails.mothers_name}, ${parentDetails.mothers_middle_name},
        ${parentDetails.mothers_last_name}, ${parentDetails.mothers_qualification},
        ${parentDetails.mothers_occupation}, ${parentDetails.mothers_aadhaar_no},
        ${parentDetails.contact_no_1}, ${parentDetails.contact_no_2},
        ${parentDetails.family_income}, ${parentDetails.part_of_ngo},
        'PENDING', NOW(), NOW()
      )
    `;

    return {
      id: studentId,
      message: 'Student created successfully',
    };
  } catch (error) {
    console.error('Error creating student:', error);
    throw new Error('Failed to create student');
  }
};

export const createStudentDetailsService = async (
  data: CreateStudentDetailsInput,
  classId: string,
  photoPath?: string
) => {
  try {
    // Generate UUID for the new record
    const studentId = require('crypto').randomUUID();

    // Create partial student admission record using raw SQL (temporary fix)
    await prisma.$executeRaw`
      INSERT INTO "StudentAdmission" (
        id, "classId", "grNo", "familyName", "firstName", "middleName", "lastName",
        gender, "dateOfBirth", age, "contactNo", email, "aadhaarNo", "bloodGroup",
        photo, "birthPlace", "motherTongue", address, city, pin, district, state,
        country, religion, caste, "subCaste", department, classroom, year, route,
        waypoint, "fathersName", "fathersMiddleName", "fathersLastName",
        "mothersName", "mothersMiddleName", "mothersLastName", "contactNo1",
        status, "createdAt", "updatedAt"
      ) VALUES (
        ${studentId}, ${classId}, ${data.gr_no}, ${data.family_name},
        ${data.first_name}, ${data.middle_name}, ${data.last_name},
        ${data.gender}, ${new Date(data.date_of_birth)}, ${data.age},
        ${data.contact_no}, ${data.email}, ${data.aadhaar_no}, ${data.blood_group},
        ${photoPath}, ${data.birth_place}, ${data.mother_tongue}, ${data.address},
        ${data.city}, ${data.pin}, ${data.district}, ${data.state}, ${data.country},
        ${data.religion}, ${data.caste}, ${data.sub_caste}, ${data.department},
        ${data.classroom}, ${data.year}, ${data.route}, ${data.waypoint},
        '', '', '', '', '', '', '', 'PENDING', NOW(), NOW()
      )
    `;

    return {
      id: studentId,
      message: 'Student details created successfully',
    };
  } catch (error) {
    console.error('Error creating student details:', error);
    throw new Error('Failed to create student details');
  }
};

export const createParentDetailsService = async (
  data: CreateParentDetailsInput,
  studentId: string
) => {
  try {
    // Update the existing student admission record with parent details using raw SQL
    await prisma.$executeRaw`
      UPDATE "StudentAdmission" SET
        "fathersName" = ${data.fathers_name},
        "fathersMiddleName" = ${data.fathers_middle_name},
        "fathersLastName" = ${data.fathers_last_name},
        "fathersQualification" = ${data.fathers_qualification},
        "fathersOccupation" = ${data.fathers_occupation},
        "fathersAadhaarNo" = ${data.fathers_aadhaar_no},
        "mothersName" = ${data.mothers_name},
        "mothersMiddleName" = ${data.mothers_middle_name},
        "mothersLastName" = ${data.mothers_last_name},
        "mothersQualification" = ${data.mothers_qualification},
        "mothersOccupation" = ${data.mothers_occupation},
        "mothersAadhaarNo" = ${data.mothers_aadhaar_no},
        "contactNo1" = ${data.contact_no_1},
        "contactNo2" = ${data.contact_no_2},
        "familyIncome" = ${data.family_income},
        "partOfNgo" = ${data.part_of_ngo},
        "updatedAt" = NOW()
      WHERE id = ${studentId}
    `;

    return {
      message: 'Parent details created successfully',
    };
  } catch (error) {
    console.error('Error creating parent details:', error);
    throw new Error('Failed to create parent details');
  }
};

export const checkGRNumberService = async (grNumber: string, classId: string) => {
  try {
    const existingStudent = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM "StudentAdmission"
      WHERE "grNo" = ${grNumber} AND "classId" = ${classId}
    `;

    const count = Array.isArray(existingStudent) && existingStudent[0] ? Number(existingStudent[0].count) : 0;

    return {
      exists: count > 0,
      message: count > 0 ? 'GR Number already exists' : 'GR Number is available',
    };
  } catch (error) {
    console.error('Error checking GR number:', error);
    throw new Error('Failed to check GR number');
  }
};

// Service to update admission status (temporarily simplified)
export const updateAdmissionStatusService = async (
  admissionId: string,
  status: 'PENDING' | 'APPROVED' | 'REJECTED',
  classId?: string
) => {
  try {
    // Update using raw SQL (temporary fix)
    if (classId) {
      await prisma.$executeRaw`
        UPDATE "StudentAdmission" SET
          status = ${status},
          "updatedAt" = NOW()
        WHERE id = ${admissionId} AND "classId" = ${classId}
      `;
    } else {
      await prisma.$executeRaw`
        UPDATE "StudentAdmission" SET
          status = ${status},
          "updatedAt" = NOW()
        WHERE id = ${admissionId}
      `;
    }

    return {
      id: admissionId,
      status: status,
      message: `Admission ${status.toLowerCase()} successfully`,
    };
  } catch (error) {
    console.error('Error updating admission status:', error);
    throw new Error('Failed to update admission status');
  }
};

// Service to get all admissions for a class (temporarily simplified)
export const getAllAdmissionsService = async (
  classId: string,
  options: StudentFilterOptions = {}
) => {
  try {
    const { page = 1, limit = 10, status, search } = options;
    const offset = (page - 1) * limit;

    // Get admissions using raw SQL (temporary fix)
    let whereCondition = `"classId" = '${classId}'`;

    if (status) {
      whereCondition += ` AND status = '${status}'`;
    }

    if (search) {
      whereCondition += ` AND ("firstName" ILIKE '%${search}%' OR "lastName" ILIKE '%${search}%' OR email ILIKE '%${search}%' OR "grNo" ILIKE '%${search}%')`;
    }

    // Build the query dynamically to avoid SQL injection
    let baseQuery = `
      SELECT id, "grNo", "firstName", "middleName", "lastName", email, "contactNo",
             department, classroom, status, "createdAt"
      FROM "StudentAdmission"
      WHERE "classId" = $1
    `;

    let countQuery = `SELECT COUNT(*) as count FROM "StudentAdmission" WHERE "classId" = $1`;
    let params = [classId];
    let paramIndex = 2;

    if (status) {
      baseQuery += ` AND status = $${paramIndex}`;
      countQuery += ` AND status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (search) {
      baseQuery += ` AND ("firstName" ILIKE $${paramIndex} OR "lastName" ILIKE $${paramIndex} OR email ILIKE $${paramIndex} OR "grNo" ILIKE $${paramIndex})`;
      countQuery += ` AND ("firstName" ILIKE $${paramIndex} OR "lastName" ILIKE $${paramIndex} OR email ILIKE $${paramIndex} OR "grNo" ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    baseQuery += ` ORDER BY "createdAt" DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);

    const [admissions, totalCountResult] = await Promise.all([
      prisma.$queryRawUnsafe(baseQuery, ...params),
      prisma.$queryRawUnsafe(countQuery, ...params.slice(0, -2)) // Remove limit and offset for count
    ]);

    const totalCount = Array.isArray(totalCountResult) && totalCountResult[0]
      ? Number(totalCountResult[0].count)
      : 0;

    return {
      admissions,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  } catch (error) {
    console.error('Error getting admissions:', error);
    throw new Error('Failed to get admissions');
  }
};
