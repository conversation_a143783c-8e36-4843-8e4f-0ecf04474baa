{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon --watch src --exec ts-node -r tsconfig-paths/register src/index.ts", "start": "node dist/index.js", "build": "tsc && tsc-alias", "lint": "eslint . --ext .js,.ts"}, "prisma": {"seed": "ts-node prisma/constantSeed.ts", "schema": "src/prisma/schema.prisma"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.5.0", "@types/nodemailer": "^6.4.17", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "razorpay": "^2.9.6", "socket.io": "^4.8.1", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "nodemon": "^3.1.9", "prisma": "^6.5.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.15", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}}