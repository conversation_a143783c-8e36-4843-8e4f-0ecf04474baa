<?php

use Admission\Http\Controllers\AdmissionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth'])->group(function () {
    Route::resource('student', AdmissionController::class);
    Route::post('/create-students-details', [AdmissionController::class, 'storeStudentDetails'])->name('storeStudentDetails');
    Route::post('/create-students-parents-details', [AdmissionController::class, 'storeStudentParentsDetails'])->name('storeStudentParentsDetails');

    Route::post('/export-student', [AdmissionController::class, 'exportStudents'])->name('export-student');

    Route::post('/student-parent-details', [AdmissionController::class, 'getStudentParentDetailsFromContactNo'])->name('studentParentDetails');
    Route::get('/search-student', [AdmissionController::class, 'searchStudent'])->name('searchStudent');
    Route::post('/leaving-certificare/{student_id}', [AdmissionController::class, 'generateLeavingCertificate'])->name('generateLC');
    Route::get('/leaving-certificare-view/{student_id}', [AdmissionController::class, 'getLeavingCertificate'])->name('getLeavingCertificate');
    Route::get('/student-active-inactive/{student_id}', [AdmissionController::class, 'activeInactive'])->name('student.activeInactive');
    Route::get('/get-students-by-classroom', [AdmissionController::class, 'getStudentsByClassroom'])->name('getStudentsByClassroom');
    Route::get('/check-grno', [AdmissionController::class, 'checkGRNO'])->name('checkGRNO');

    Route::get('/show-routes-to-student/{student_id}', [AdmissionController::class, 'showRoutesToStudent'])->name('show.routes-to-student');
    Route::post('/save-routes-to-student/{student_id}', [AdmissionController::class, 'saveRoutesToStudent'])->name('save.routes-to-student');
});
