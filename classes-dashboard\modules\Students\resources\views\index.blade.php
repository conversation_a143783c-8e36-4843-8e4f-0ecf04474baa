@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Students</h1>
                </div>
                <div class="col-sm-6">
                    <div class="float-sm-right">
                        <a href="{{ route('students.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Student
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Student Profiles</h3>
                    </div>
                    <div class="card-body">
                        <table id="students_table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Contact</th>
                                    <th>School</th>
                                    <th>Classroom</th>
                                    <th>Status</th>
                                    <th>Created At</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via DataTables AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
    <script>
        var studentsRoute = {
            index: "{{ route('students.index') }}",
            delete: "{{ route('students.destroy', ':id') }}",
        };

        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#students_table').DataTable({
                processing: true,
                serverSide: false, // We'll load data via AJAX from Node.js
                ajax: {
                    url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/student-profile/admin/all',
                    type: 'GET',
                    xhrFields: {
                        withCredentials: true
                    },
                    dataSrc: function(json) {
                        return json.data || [];
                    },
                    error: function(xhr, error, code) {
                        console.error('Error loading students:', error);
                        toastr.error('Failed to load students data');
                        return [];
                    }
                },
                columns: [
                    {
                        data: null,
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return `
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-info" onclick="viewStudent('${row.id}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning" onclick="editStudent('${row.id}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger deleteStudentEntry" data-deletestudentid="${row.id}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            `;
                        }
                    },
                    {
                        data: null,
                        name: 'name',
                        render: function(data, type, row) {
                            return `${row.student?.firstName || ''} ${row.student?.lastName || ''}`.trim();
                        }
                    },
                    { data: 'student.email', name: 'email' },
                    { data: 'student.contact', name: 'contact' },
                    { data: 'school', name: 'school' },
                    { data: 'classroom', name: 'classroom' },
                    {
                        data: 'status',
                        name: 'status',
                        render: function(data, type, row) {
                            const statusClass = data === 'APPROVED' ? 'success' : 
                                              data === 'REJECTED' ? 'danger' : 'warning';
                            return `<span class="badge badge-${statusClass}">${data}</span>`;
                        }
                    },
                    {
                        data: 'createdAt',
                        name: 'createdAt',
                        render: function(data, type, row) {
                            return new Date(data).toLocaleDateString();
                        }
                    }
                ],
                order: [[7, 'desc']] // Order by created date
            });

            // Delete student functionality
            $(document).on('click', '.deleteStudentEntry', function() {
                var studentId = $(this).attr('data-deletestudentid');
                
                if (confirm('Are you sure you want to delete this student profile?')) {
                    $.ajax({
                        url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/student-profile/admin/' + studentId,
                        method: 'DELETE',
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function(response) {
                            toastr.success('Student profile deleted successfully!');
                            table.ajax.reload();
                        },
                        error: function(xhr, status, error) {
                            const message = xhr.responseJSON?.message || 'Failed to delete student profile.';
                            toastr.error(message);
                        }
                    });
                }
            });
        });

        function viewStudent(studentId) {
            // Implement view functionality
            window.open('{{ env('UEST_FRONTEND_URL') }}/student-profile/' + studentId, '_blank');
        }

        function editStudent(studentId) {
            // Implement edit functionality
            window.location.href = "{{ route('students.edit', ':id') }}".replace(':id', studentId);
        }
    </script>
@endsection
